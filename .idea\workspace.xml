<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="e93226c6-ae16-42b4-88f1-a3172245fde9" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerConfigs">
    <option name="configs">
      <option value="$PROJECT_DIR$/common/components/PDFMerge/TCPDF/composer.json" />
    </option>
  </component>
  <component name="ComposerSettings" synchronizationState="SYNCHRONIZE">
    <pharConfigPath>$PROJECT_DIR$/composer.json</pharConfigPath>
    <execution />
  </component>
  <component name="PhpWorkspaceProjectConfiguration" interpreter_name="D:\Program Files\php\php-5.6.40-Win32-VC11-x64\php.exe">
    <include_path>
      <path value="$PROJECT_DIR$/vendor/PHPWord/Writer" />
      <path value="$PROJECT_DIR$/vendor/PHPWord/Style" />
      <path value="$PROJECT_DIR$/vendor/PHPWord/Shared" />
      <path value="$PROJECT_DIR$/vendor/PHPWord/Section" />
      <path value="$PROJECT_DIR$/vendor/PHPWord/_staticDocParts" />
      <path value="$PROJECT_DIR$/vendor/Excel/PHPExcel" />
      <path value="$PROJECT_DIR$/vendor/phpseclib/phpseclib" />
      <path value="$PROJECT_DIR$/vendor/bower-asset/jquery" />
      <path value="$PROJECT_DIR$/vendor/bower-asset/typeahead.js" />
      <path value="$PROJECT_DIR$/vendor/bower-asset/jquery.inputmask" />
      <path value="$PROJECT_DIR$/vendor/bower-asset/yii2-pjax" />
      <path value="$PROJECT_DIR$/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/vendor/bower-asset/punycode" />
      <path value="$PROJECT_DIR$/vendor/bower/jquery" />
      <path value="$PROJECT_DIR$/vendor/bower-asset/bootstrap" />
      <path value="$PROJECT_DIR$/vendor/bower/typeahead.js" />
      <path value="$PROJECT_DIR$/vendor/bower/jquery.inputmask" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/RichText" />
      <path value="$PROJECT_DIR$/vendor/bower/yii2-pjax" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/Writer" />
      <path value="$PROJECT_DIR$/vendor/bower/punycode" />
      <path value="$PROJECT_DIR$/vendor/ezyang/htmlpurifier" />
      <path value="$PROJECT_DIR$/vendor/textalk/websocket" />
      <path value="$PROJECT_DIR$/vendor/bower/bootstrap" />
      <path value="$PROJECT_DIR$/vendor/fzaninotto/faker" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/Style" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/CachedObjectStorage" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/CalcEngine" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/Calculation" />
      <path value="$PROJECT_DIR$/vendor/php-amqplib/php-amqplib" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/Worksheet" />
      <path value="$PROJECT_DIR$/vendor/react/cache" />
      <path value="$PROJECT_DIR$/vendor/evenement/evenement" />
      <path value="$PROJECT_DIR$/vendor/react/dns" />
      <path value="$PROJECT_DIR$/vendor/tcpdf/examples" />
      <path value="$PROJECT_DIR$/vendor/react/promise" />
      <path value="$PROJECT_DIR$/vendor/tcpdf/include" />
      <path value="$PROJECT_DIR$/vendor/react/socket" />
      <path value="$PROJECT_DIR$/vendor/tcpdf/tools" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/Reader" />
      <path value="$PROJECT_DIR$/vendor/react/stream" />
      <path value="$PROJECT_DIR$/vendor/react/event-loop" />
      <path value="$PROJECT_DIR$/vendor/tcpdf/config" />
      <path value="$PROJECT_DIR$/vendor/tcpdf/fonts" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/Shared" />
      <path value="$PROJECT_DIR$/vendor/cboden/ratchet" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/Chart" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/Cell" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/locale" />
      <path value="$PROJECT_DIR$/vendor/pclzip/pclzip" />
      <path value="$PROJECT_DIR$/vendor/laminas/laminas-escaper" />
      <path value="$PROJECT_DIR$/vendor/laminas/laminas-zendframework-bridge" />
      <path value="$PROJECT_DIR$/vendor/ratchet/rfc6455" />
      <path value="$PROJECT_DIR$/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/vendor/paragonie/random_compat" />
      <path value="$PROJECT_DIR$/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/vendor/composer" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-composer" />
      <path value="$PROJECT_DIR$/vendor/phpdiff/Diff" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-swiftmailer" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-gii" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-bootstrap" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-mongodb" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-codeception" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-faker" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-debug" />
      <path value="$PROJECT_DIR$/vendor/cebe/markdown" />
      <path value="$PROJECT_DIR$/vendor/phpspec/php-diff" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php70" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-foundation" />
      <path value="$PROJECT_DIR$/vendor/phpoffice/phpword" />
      <path value="$PROJECT_DIR$/vendor/symfony/routing" />
      <path value="$PROJECT_DIR$/vendor/phpoffice/common" />
      <path value="$PROJECT_DIR$/vendor/spout-2.7.3/src" />
      <path value="$PROJECT_DIR$/vendor/swiftmailer/swiftmailer" />
    </include_path>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2x1uX9xQcsarpEPactQjLgDlePw" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/integle2025/eln_5.3.11_dev&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\integle2025\\eln_5.3.11_dev\\frontend\\web\\in_material\\node_modules\\typescript\\lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\integle2025\eln_5.3.11_dev\frontend\web\vue-ui\src\assets" />
      <recent name="D:\integle2025\eln_5.3.11_dev\frontend\web\vue-ui\src\components" />
      <recent name="D:\integle2025\eln_5.3.11_dev\frontend\web\js\bin" />
      <recent name="D:\integle2025\eln_5.3.11_dev\frontend\web\js" />
      <recent name="D:\integle2025\eln_5.3.11_dev\frontend\web\vue-ui\src" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\integle2025\eln_5.3.11_dev\frontend\web\js\bin" />
      <recent name="D:\integle2025\eln_5.3.11_dev\frontend\web\vue-ui\src" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-PS-241.18034.69" />
        <option value="bundled-php-predefined-ba97393d7c68-48a1a656d44e-com.jetbrains.php.sharedIndexes-PS-241.18034.69" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
    <supportedVersion>125</supportedVersion>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="e93226c6-ae16-42b4-88f1-a3172245fde9" name="Changes" comment="" />
      <created>1747117937939</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1747117937939</updated>
      <workItem from="1747117939033" duration="2100000" />
      <workItem from="1747120096357" duration="56132000" />
      <workItem from="1747309697092" duration="4351000" />
      <workItem from="1747619753344" duration="30577000" />
      <workItem from="1747967866793" duration="41060000" />
      <workItem from="1748223081360" duration="2607000" />
      <workItem from="1748225969314" duration="48670000" />
      <workItem from="1748914319565" duration="41939000" />
      <workItem from="1749176932729" duration="13071000" />
      <workItem from="1749433507348" duration="38529000" />
      <workItem from="1749727573615" duration="6654000" />
      <workItem from="1749802538357" duration="6631000" />
      <workItem from="1750038629641" duration="3125000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/frontend/web/vue-ui/src/components/InstrumentBookingCreateDialog.vue</url>
          <line>227</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>