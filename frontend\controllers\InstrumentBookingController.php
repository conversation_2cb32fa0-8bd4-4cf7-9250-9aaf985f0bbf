<?php

namespace frontend\controllers;

use frontend\controllers\MyController;
use frontend\interfaces\CenterInterface;
use frontend\models\InstrumentsBookModel;
use frontend\services\InstrumentServer;
use yii\helpers\ArrayHelper;

class InstrumentBookingController extends MyController
{
    public function actionGetInstrumentsData()
    {
        $postData = \Yii::$app->getRequest()->post();

        // 用于查询我加入的鹰群或部门
        $currentUserGroupIds = array_column($this->userinfo->groups, 'id');
        $currentUserDepartmentIds = array_column($this->userinfo->department, 'id');
        $where['groups'] = $currentUserGroupIds;
        $where['departments'] = $currentUserDepartmentIds;

        // 获取查看权限
        // $userAuth = (new CompanyAuthServer())->getCompanyAuthByUserId($this->userinfo->id);
        if (\Yii::$app->view->params['instruments_manage_read']) {
            $where['viewAuth'] = '1'; // 能查看所有仪器
        } else {
            $where['viewAuth'] = '2'; // 能查看可见鹰群和部门仪器
        }
        if (!\Yii::$app->view->params['instruments_manage_read'] && !\Yii::$app->view->params['instruments_manage_read_my']) {
            return $this->fail(\Yii::t('base', 'no_power')); // 没有查看仪器的权限
        }
        // 获取查询条件
        $curPage = isset($postData['curPage']) ? $postData['curPage'] : 1;
        $limit = isset($postData['pageSize']) ? $postData['pageSize'] : \Yii::$app->params['default_page_size'];
        $where['instrumentName'] = isset($postData['instrumentName']) ? $postData['instrumentName'] : '';
        $where['bookTime'] = isset($postData['bookTime']) ? $postData['bookTime'] : '';
        $where['onlyBooked'] = isset($postData['onlyBooked']) ? $postData['onlyBooked'] : false;
        $where['lang'] = isset($postData['lang']) ? $postData['onlyBooked'] : 'cn';
        // 获取查询的数据
        $data = (new InstrumentServer())->queryInstrumentsWithBooking($where, $limit, $curPage);
        return $this->success($data);
    }

    /**
     * Notes: 获取我的预约记录页面下所需要的数据 以及 用户权限信息（给inform匹配）
     * Author: ChenQi
     * Date: 2025/6/10 18:15
     * @return \common\controllers\json
     */
    public function actionDeleteInstrumentBooking()
    {
        $postData = \Yii::$app->getRequest()->post();
        $id = isset($postData['id']) ? $postData['id'] : 1;
        $bookingQuery = InstrumentsBookModel::find()->from(InstrumentsBookModel::tableName());
    }

    /**
     * Notes: 获取我的预约记录页面下所需要的数据 以及 用户权限信息（给inform匹配）
     * Author: zsm
     * Date: 2025/6/5 14:30
     * @return \common\controllers\json
     */
    public function actionGetMyInstrumentBookingInfo()
    {

        $create_by = $this->userinfo->id; // 只查询我自己的预约

        // 获取我的预约记录数据（不分页）
        $bookingData = (new InstrumentServer())->listMyBook($create_by);

        // 判断借用记录当前的状态 1 已预约 2 已撤销 3 使用者 4 已完成
        $currentTime = date('Y-m-d H:i:s'); // 获取当前时间

        foreach ($bookingData['my_book_list'] as &$book) {
            if ($book['status'] == 0) {
                $book['status'] = 2; // 如果 status 为 0，则修改为 2
            } elseif ($book['status'] == 1) { // 只有当 status 为 1 时才需要判断
                if (strtotime($currentTime) < strtotime($book['start_time'])) {
                    $book['status'] = 1; // 当前时间小于 start_time，status 仍为 1
                } elseif (strtotime($currentTime) > strtotime($book['end_time'])) {
                    $book['status'] = 4; // 当前时间大于 end_time，status 设置为 4
                } else {
                    $book['status'] = 3; // 当前时间介于 start_time 和 end_time 之间，status 设置为 3
                }
            }
        }

        // 获取用户信息数据
        $allUsers = array_unique(array_merge(
            array_column($bookingData['my_book_list'], 'update_by'),
            array_column($bookingData['my_book_list'], 'create_by')
        ));

        $usersRes = (new CenterInterface())->userDetailsByUserIds($allUsers);

        return $this->success(['bookingList' => $bookingData, 'extra' => $usersRes]);
    }

    /**
     * Notes: 仪器库管理 -> 预约记录 inform 查询接口 有权限的仪器的预约记录
     * Author: zsm
     * Date: 2025/6/6 17:30
     * @return \common\controllers\json
     */
    public function actionGetInstrumentBookingInfo()
    {
        // TODO
        $postData = \Yii::$app->getRequest()->post();

        // 用于查询我加入的鹰群或部门
        $currentUserGroupIds = array_column($this->userinfo->groups, 'id');
        $currentUserDepartmentIds = array_column($this->userinfo->department, 'id');
        $where['groups'] = $currentUserGroupIds;
        $where['departments'] = $currentUserDepartmentIds;

        // 获取查看权限
        // $userAuth = (new CompanyAuthServer())->getCompanyAuthByUserId($this->userinfo->id);
        if (\Yii::$app->view->params['instruments_manage_read']) {
            $where['viewAuth'] = '1'; // 能查看所有仪器
        } else {
            $where['viewAuth'] = '2'; // 能查看可见鹰群和部门仪器
        }

        if (!\Yii::$app->view->params['instruments_manage_read'] && !\Yii::$app->view->params['instruments_manage_read_my']) {
            return $this->fail(\Yii::t('base', 'no_power')); // 没有查看仪器的权限
        }
        // 是否可编辑
        $isCanWrite = \Yii::$app->view->params['instruments_manage_write'];

        $bookingData = (new InstrumentServer())->queryInstrumentsManageWithBooking($where);

        $file = $this->renderAjax('/setting/instrument_book_record.php', []);


        // 获取用户信息数据
        $allUsers = array_unique(array_merge(
            array_column($bookingData['my_book_list'], 'update_by'),
            array_column($bookingData['my_book_list'], 'create_by')
        ));

        $usersRes = (new CenterInterface())->userDetailsByUserIds($allUsers);


        return $this->success(['contentHtml' => $file, 'bookingList' => $bookingData, 'extra' => $usersRes, 'isCanWrite' => $isCanWrite]);

    }


}