import { c as t } from "./vue-i18n.js";
const a = { cancel: "Cancel", confirm: "Confirm", delete: "Delete", deleteSuccess: "Deleted successfully", unknownError: "Unknown error", updateSuccess: "Updated successfully", loading: "Loading..." }, o = { title: "Login", username: "<PERSON><PERSON><PERSON>", password: "Password", btnLogin: "Login", btnCancel: "Cancel" }, i = { create: "Create Booking", edit: "Edit Booking", name: "Instrument Booking", time: "Booking Time", warn: "Reminder", book_num: "Record Book Page", remark: "Remark", btnSure: "Confirm", btnCancel: "Cancel", min: "Minute", hour: "Hour", day: "Day", tips1: "Search by Instrument Name/Instrument ID", start_time: "Start Time", end_time: "End Time", warn0: "No Reminder", warn5m: "Reminder 5 Minutes Before", warn15m: "Reminder 15 Minutes Before", warn30m: "Reminder 30 Minutes Before", warn1h: "Reminder 1 Hour Before", warn2h: "Reminder 2 Hours Before", warn1d: "Reminder 1 Day Before", warnP: "Please Select", bookP: "Please Enter Record Book Page", cancel: "Cancel", sure: "Confirm", today: "Today", nowBook: "Current Booking", errorAlready: "This time slot is already booked", errorMax1: "This instrument is only available for", errorMax2: "bookings per day", errorMin1: "This instrument requires at least", errorMin2: "advance booking", errorMaxDuration: "The maximum booking duration for this instrument is", errorAvailable: "Please modify within available booking hours", error: "Please select a time after the current time", editS: "Edit Successful", editError: "Edit Failed", createS: "Creation Successful", createError: "Creation Failed", otherBook1: "Exceeds available booking time", otherBook2: "The available booking time for this instrument is", otherBook3: "Would you like to adjust your booking time and split it into" }, n = { title: "Set Booking Rules", available_slots: "Available Time Slots", start_time: "Start Time", end_time: "End Time", max_advance_day: "Max Advance Booking Days", min_advance: "Minimum Advance Booking", noLimited: "No Limit", limited: "Limited", select: "Please Select", max_booking_duration: "Max Booking Duration", cancel: "Cancel", sure: "Confirm", tips1: "Please enter the correct maximum advance booking days", tips2: "You must select a time unit", tips3: "Please enter the correct minimum advance booking days", tips4: "Please enter the correct maximum booking duration", tips5: "Please enter the correct booking time slot", tips6: "Please select a valid time", tips7: "The time slot overlaps with an existing one, please select a non-overlapping time", tips8: "The time slot overlaps with an existing one, but the settings are successful", min: "Minutes", hour: "Hours", day: "Days" }, r = { day: "day", week: "week", dateFormat: "YYYY-MM-DD", today: "Today", yesterday: "Yesterday", weekAgo: "A week ago", editBooking: "Edit Booking", bookAgain: "Book Again", cancelBooking: "Cancel Booking", expandRecord: "Expand Record" }, s = { toggle: "Toggle dark mode" }, l = { tableName: "Instruments book" }, d = { noData: "No Data", noAuth: "No Permission" }, m = {
  common: a,
  modalA: o,
  InstrumentBookingCreateDialog: i,
  InstrumentBookingConfigDialog: n,
  bookInstruments: r,
  theme: s,
  instrumentsBookMine: l,
  empty: d
}, c = { cancel: "取消", confirm: "确定", delete: "删除", deleteSuccess: "删除成功", unknownError: "未知错误", updateSuccess: "更新成功", loading: "加载中..." }, u = { title: "登录", username: "用户名", password: "密码", btnLogin: "登录", btnCancel: "取消" }, p = { create: "创建预约", edit: "编辑预约", name: "预约仪器", time: "预约时间", warn: "提醒", book_num: "记录本页码", remark: "备注", btnSure: "确认", btnCancel: "取消", min: "分钟", hour: "小时", day: "天", tips1: "查找仪器名称/仪器ID", start_time: "开始时间", end_time: "结束时间", warn0: "不提醒", warn5m: "提前5分钟提醒", warn15m: "提前15分钟提醒", warn30m: "提前30分钟提醒", warn1h: "提前1小时提醒", warn2h: "提前2小时提醒", warn1d: "提前1天提醒", warnP: "请选择", bookP: "请输入记录本页码", cancel: "取消", sure: "确认", today: "今日", nowBook: "当前预约", errorAlready: "该时间段内已有人预约", errorMax1: "该仪器仅开放", errorMax2: "天内的预约", errorMin1: "该仪器需要至少提前", errorMin2: "预约", errorMaxDuration: "该仪器每次做多预约", errorAvailable: "请在可预约时间对内修改", error: "请选择当前时间只后的时间", editS: "编辑成功", editError: "编辑失败", createS: "创建成功", createError: "创建失败", otherBook1: "超出可预约时段", otherBook2: "该仪器可预约时间段为", otherBook3: "是否将您的预约时间调整并拆分为" }, g = { title: "设置预约规则", available_slots: "可预约时间段", start_time: "开始时间", end_time: "结束时间", max_advance_day: "最多提前预约天数", min_advance: "至少提前预约", noLimited: "不限", limited: "限制", select: "请选择", max_booking_duration: "单词预约时间上限", cancel: "取消", sure: "确认", tips1: "请正确填写最大提前天数", tips2: "必须选择时间单位", tips3: "请正确填写最小提前预约天数", tips4: "请正确填写最大预约时长值", tips5: "请正确填写预约时间段", tips6: "请正确选择时间", tips7: "已有的时间段重叠，请选择不重叠的时间", tips8: "设置成功", min: "分钟", hour: "小时", day: "天" }, y = { day: "日", week: "周", dateFormat: "YYYY年MM月DD日", today: "今天", yesterday: "昨天", weekAgo: "一周前", editBooking: "编辑预约", bookAgain: "再次预约", cancelBooking: "取消预约", expandRecord: "展开详情" }, b = { toggle: "Toggle dark mode" }, h = { tableName: "预约仪器" }, k = { noData: "暂无数据", noAuth: "暂无权限" }, f = {
  common: c,
  modalA: u,
  InstrumentBookingCreateDialog: p,
  InstrumentBookingConfigDialog: g,
  bookInstruments: y,
  theme: b,
  instrumentsBookMine: h,
  empty: k
}, T = { day: "Day", week: "Week", dayAbbr: "D", weekAbbr: "W", dateFormat: "YYYY/MM/DD", today: "Today", yesterday: "Yesterday", weekAgo: "A week ago", searchInstrument: "Instrument Name/ID", state: "Status", responsible: "Responsible Person", position: "Location", bookableTime: "Available Time Slots", singleTimeLimit: "Single Booking Limit", advance: "Minimum Advance", microscope: "Microscope", bookingConflict: "Conflicts with existing bookings, cannot continue!", exit: "Exit", title: "Instrument Booking", onlyShowMine: "Show bookings", createBooking: "Create Booking", monday: "Mon", tuesday: "Tue", wednesday: "Wed", thursday: "Thu", friday: "Fri", saturday: "Sat", sunday: "Sun" }, w = {
  bookInstruments: T
}, M = { day: "日", week: "周", dayAbbr: "日", weekAbbr: "周", dateFormat: "YYYY年MM月DD日", today: "今天", yesterday: "昨天", weekAgo: "一周前", searchInstrument: "仪器名称/仪器ID", state: "状态", responsible: "责任人", position: "位置", bookableTime: "可预约时段", singleTimeLimit: "单次预约上限", advance: "至少提前", microscope: "显微镜", bookingConflict: "将包含其他预约，不可继续预定！", exit: "退出", title: "仪器预约", onlyShowMine: "仅显示我预约过的", createBooking: "创建预约", monday: "周一", tuesday: "周二", wednesday: "周三", thursday: "周四", friday: "周五", saturday: "周六", sunday: "周日" }, D = {
  bookInstruments: M
}, v = { title: "Template Type Management", existingTypes: "Existing Types", loading: "Loading...", typePlaceholder: "Enter type name", rename: "Rename", delete: "Delete", createType: "Create {type} Type", typeNamePlaceholder: "Enter template type names, one per line...", deleteConfirmMessage: "After deletion, templates under this type will be categorized as unclassified. Are you sure you want to delete?", deleteFailure: "Failed to delete template type", deleteError: "Error deleting template type", enterTypeName: "Please enter template type name", enterValidTypeName: "Please enter valid template type name", batchAddRequestData: "Batch add template type request data:", batchAddResponse: "Batch add template type response:", addFailure: "Failed to add template type", batchAddError: "Error adding template types", typeNameRequired: "Type name cannot be empty", updateFailure: "Failed to update template type", response: "Response:", duplicateTypeName: "Type name cannot be duplicated", typeNameTooLong: "Type name maximum length is 100 characters", updateError: "Error updating template type", noData: "No data", fullTemplate: "FullText Template", intextTemplate: "Intext Template", intableTemplate: "Intable Template" }, A = {
  templateTypeManager: v
}, B = { title: "模板类型管理", existingTypes: "已有类型", loading: "加载中...", typePlaceholder: "请输入类型名称", rename: "重命名", delete: "删除", createType: "创建{type}类型", typeNamePlaceholder: "输入模板类型名称，每行一个...", deleteConfirmMessage: "删除后该类型下模板将被划分为未分类，确定删除吗？", deleteFailure: "删除模板类型失败", deleteError: "删除模板类型出错", enterTypeName: "请输入模板类型名称", enterValidTypeName: "请输入有效的模板类型名称", batchAddRequestData: "批量添加模板类型请求数据:", batchAddResponse: "批量添加模板类型响应:", addFailure: "添加模板类型失败", batchAddError: "批量添加模板类型出错", typeNameRequired: "类型名称不能为空", updateFailure: "更新模板类型失败", response: "响应:", duplicateTypeName: "类型名称不能重复", typeNameTooLong: "类型名称最大为100字符", updateError: "更新模板类型出错", noData: "暂无类型", fullTemplate: "全文模板", intextTemplate: "文本方法模板", intableTemplate: "表格方法模板" }, S = {
  templateTypeManager: B
}, _ = { title: "Template General Settings", templateEffectMode: "Template Effect Mode", immediateMode: "Immediate Effect Mode", immediateModeDesc: "Templates can be used immediately after creation/modification, no need to manually publish;<br>Templates are locked during re-editing and review;<br>Suitable for scenarios requiring quick template adjustments without version tracking.", explicitMode: "Publish Control Mode", explicitModeDesc: "Templates need to be published to take effect;<br>Previous effective version can be used during re-editing and review;<br>Suitable for scenarios with template version management requirements.", immediateModeSwitchConfirm: "After switching, templates can be used without publishing.", explicitModeSwitchConfirm: "After switching, existing templates need to be published to be used.", switchModeConfirmTitle: "Are you sure to switch to {mode}?", switchedToMode: "Switched to {mode}", switchCancelled: "Switch cancelled", fetchFailed: "Failed to get template effect mode settings" }, C = {
  templateGeneralSettings: _
}, x = { title: "模板通用设置", templateEffectMode: "模板生效模式", immediateMode: "即时生效模式", immediateModeDesc: "模板【创建/修改】成功后即可使用，无需手动【发布】；<br>模板重新编辑审核期间处于锁定状态，不可以使用；<br>适用于需要快速调整模板且无需版本追溯的场景。", explicitMode: "发布管控模式", explicitModeDesc: "模板需要通过【发布】操作才会生效；<br>模板重新编辑审核期间，可调用上一个生效版本；<br>适用于有模板版本管理需求场景。", immediateModeSwitchConfirm: "切换后，模板无需【发布】即可使用。", explicitModeSwitchConfirm: "切换后，原有模板需要【发布】才可以使用。", switchModeConfirmTitle: "确定要切换到{mode}吗？", switchedToMode: "已切换为{mode}", switchCancelled: "已取消切换", fetchFailed: "获取模板生效模式设置失败" }, E = {
  templateGeneralSettings: x
}, P = { title: "Template History", allHistory: "All History", versions: "Versions", operationTime: "Operation Time", operator: "Operator", operation: "Operation", details: "Details", version: "Version", effectiveDate: "Effective Date", reason: "Reason", transferFrom: "Transfer From", transferTo: "Transfer To", actions: { create: "Create Template", save: "Save Template", publish: "Publish Template", remove: "Move to Trash", restore: "Restore from Trash", transfer: "Transfer Template", submit_audit: "Submit Template Audit", cancel_audit: "Withdraw Template Audit", audit_agree: "Template Audit Approved", audit_refuse: "Template Audit Rejected", submit_publish_audit: "Submit Publish Audit", publish_audit_agree: "Publish Audit Approved", publish_audit_refuse: "Publish Audit Rejected", cancel_publish: "Cancel Publish", re_edit: "Re-edit" } }, I = {
  templateHistoryDialog: P
}, R = { title: "模板痕迹", allHistory: "全部痕迹", versions: "版本", operationTime: "操作时间", operator: "操作人", operation: "操作", details: "详情", version: "版本号", effectiveDate: "生效日期", reason: "原因", transferFrom: "转让人", transferTo: "被转让人", actions: { create: "创建模板", save: "保存模板", publish: "发布模板", remove: "移入回收站", restore: "从回收站恢复", transfer: "转让模板", submit_audit: "提交模板审核", cancel_audit: "撤回模板审核", audit_agree: "模板审核通过", audit_refuse: "模板审核驳回", submit_publish_audit: "提交发布审核", publish_audit_agree: "发布审核通过", publish_audit_refuse: "发布审核驳回", cancel_publish: "取消发布", re_edit: "重新编辑" } }, N = {
  templateHistoryDialog: R
}, F = {
  "en-US": { ...m, ...w, ...A, ...C, ...I },
  "zh-CN": { ...f, ...D, ...S, ...E, ...N }
}, L = {
  en: "en-US",
  zh: "zh-CN"
}, e = "zh-CN", Y = window.lang && L[window.lang], H = Y || e, q = t({
  legacy: !1,
  locale: H,
  fallbackLocale: e,
  messages: F
});
export {
  q as default
};
