import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { fileURLToPath, URL } from 'node:url'
import path from 'node:path'
import cssInjectedByJsPlugin from 'vite-plugin-css-injected-by-js'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
// 定义需要匹配同名css文件的入口文件列表
const entryPoints = {
  InstrumentBookingConfigDialog: './src/components/InstrumentBookingConfigDialog.vue',
  templateTypeManager: './src/components/template/TemplateTypeManager.vue',
  templateGeneralSettings: './src/components/template/TemplateGeneralSettings.vue',
  bookInstruments: './src/components/BookInstruments.vue',
  InstrumentBookingCreateDialog: './src/components/InstrumentBookingCreateDialog.vue',
  instrumentsBookMine: './src/components/instrumentsBookMine/instrumentsBookMine.vue',
  templateHistoryDialog: './src/components/template/TemplateHistoryDialog.vue'
};
export default defineConfig({

  define: {
    // 这行会把代码里的 process.env.NODE_ENV 替换为 "production"
    'process.env.NODE_ENV': JSON.stringify('production')
  },  root: '.', // 保证 dev.html 能被识别
  server: {
    open: '/dev.html' // 自动打开 dev.html
  },
  plugins: [
    vue(),
    cssInjectedByJsPlugin({
      // 确保CSS被正确注入
      topExecutionPriority: true, // CSS在JS执行前注入，避免闪烁
      styleId: (entryName) => `style-${entryName}`, // 为每个入口文件生成唯一的style ID
      // 自动匹配入口文件名称
      jsAssetsFilterFunction: (outputChunk) => {
        const entryNames = Object.keys(entryPoints);
        return entryNames.some(entryName =>
            outputChunk.fileName === `${entryName}.js` ||
            outputChunk.fileName.startsWith(`${entryName}-`)
        );
      }
    }),
    // https://github.com/unplugin/unplugin-auto-import
    AutoImport({
      // targets to transform
      include: [
        /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
        /\.vue$/,
        /\.vue\?vue/, // .vue
        /\.md$/, // .md
      ],
      imports: ['vue', 'vue-i18n', 'vue-router', 'pinia', '@vueuse/core'],
      dts: 'src/auto-imports.d.ts',
      vueTemplate: true,
      resolvers: [ElementPlusResolver({ importStyle: true})],
    }),
    // https://github.com/unplugin/unplugin-vue-components
    Components({
      extensions: ['vue'],
      // allow auto import and register components used in markdown
      include: [/\.vue$/, /\.vue\?vue/],
      dts: 'src/auto-components.d.ts',
      resolvers: [ElementPlusResolver({ importStyle: true})],
    }),
  ],
   resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },
  build: {
    lib: {
      entry: {
        // modalA: './src/components/ModalA.vue',
        // modalB: './src/components/ModalB.vue',
        // InstrumentBookingConfigDialog: './src/components/InstrumentBookingConfigDialog.vue',
        InstrumentBookingCreateDialog: './src/components/InstrumentBookingCreateDialog.vue',
        bookInstruments: './src/components/BookInstruments.vue',
        i18n:   './src/locales/index.js',
        // templateTypeManager: './src/components/template/TemplateTypeManager.vue',
        // instrumentsBookMine: './src/components/instrumentsBookMine/instrumentsBookMine.vue',  
        // templateHistoryDialog: './src/components/template/TemplateHistoryDialog.vue'
      },
      formats: ['es']
    },
    rollupOptions: {
      external: ['vue', 'element-plus'],
      output: {
        // 配置资源文件路径
        assetFileNames: (assetInfo) => {
          if (assetInfo.name?.endsWith('.css')) {
            return 'style/[name][extname]'
          }
          return 'assets/[name][extname]'
        },
        // 确保 CSS 被 JS 引用
        chunkFileNames: '[name].js',
        entryFileNames: '[name].js'
      }
    }
  }
  
})




