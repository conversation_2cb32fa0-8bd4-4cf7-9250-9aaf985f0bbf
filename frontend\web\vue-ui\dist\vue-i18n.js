import { effectScope as zn, ref as Ae, shallowRef as En, computed as oe, watch as Et, isRef as ea, defineComponent as vt, getCurrentInstance as Qe, h as gn, Fragment as Ln, inject as ta, onBeforeMount as na, onMounted as aa, onUnmounted as ra, createVNode as la, Text as sa } from "vue";
/*!
  * shared v9.14.4
  * (c) 2025 ka<PERSON><PERSON>
  * Released under the MIT License.
  */
const lt = typeof window < "u", Le = (e, t = !1) => t ? Symbol.for(e) : Symbol(e), ca = (e, t, n) => oa({ l: e, k: t, s: n }), oa = (e) => JSON.stringify(e).replace(/\u2028/g, "\\u2028").replace(/\u2029/g, "\\u2029").replace(/\u0027/g, "\\u0027"), j = (e) => typeof e == "number" && isFinite(e), ia = (e) => Tn(e) === "[object Date]", ge = (e) => Tn(e) === "[object RegExp]", ct = (e) => F(e) && Object.keys(e).length === 0, q = Object.assign, ua = Object.create, H = (e = null) => ua(e);
let Ft;
const me = () => Ft || (Ft = typeof globalThis < "u" ? globalThis : typeof self < "u" ? self : typeof window < "u" ? window : typeof global < "u" ? global : H());
function Mt(e) {
  return e.replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&apos;");
}
const fa = Object.prototype.hasOwnProperty;
function le(e, t) {
  return fa.call(e, t);
}
const x = Array.isArray, G = (e) => typeof e == "function", p = (e) => typeof e == "string", w = (e) => typeof e == "boolean", $ = (e) => e !== null && typeof e == "object", _a = (e) => $(e) && G(e.then) && G(e.catch), Nn = Object.prototype.toString, Tn = (e) => Nn.call(e), F = (e) => {
  if (!$(e))
    return !1;
  const t = Object.getPrototypeOf(e);
  return t === null || t.constructor === Object;
}, ma = (e) => e == null ? "" : x(e) || F(e) && e.toString === Nn ? JSON.stringify(e, null, 2) : String(e);
function da(e, t = "") {
  return e.reduce((n, r, l) => l === 0 ? n + r : n + t + r, "");
}
function ot(e) {
  let t = e;
  return () => ++t;
}
function Ea(e, t) {
  typeof console < "u" && (console.warn("[intlify] " + e), t && console.warn(t.stack));
}
const nt = (e) => !$(e) || x(e);
function rt(e, t) {
  if (nt(e) || nt(t))
    throw new Error("Invalid value");
  const n = [{ src: e, des: t }];
  for (; n.length; ) {
    const { src: r, des: l } = n.pop();
    Object.keys(r).forEach((a) => {
      a !== "__proto__" && ($(r[a]) && !$(l[a]) && (l[a] = Array.isArray(r[a]) ? [] : H()), nt(l[a]) || nt(r[a]) ? l[a] = r[a] : n.push({ src: r[a], des: l[a] }));
    });
  }
}
/*!
  * message-compiler v9.14.4
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */
function ga(e, t, n) {
  return { line: e, column: t, offset: n };
}
function st(e, t, n) {
  return { start: e, end: t };
}
const La = /\{([0-9a-zA-Z]+)\}/g;
function In(e, ...t) {
  return t.length === 1 && Na(t[0]) && (t = t[0]), (!t || !t.hasOwnProperty) && (t = {}), e.replace(La, (n, r) => t.hasOwnProperty(r) ? t[r] : "");
}
const bn = Object.assign, Ut = (e) => typeof e == "string", Na = (e) => e !== null && typeof e == "object";
function On(e, t = "") {
  return e.reduce((n, r, l) => l === 0 ? n + r : n + t + r, "");
}
const Ct = {
  USE_MODULO_SYNTAX: 1,
  __EXTEND_POINT__: 2
}, Ta = {
  [Ct.USE_MODULO_SYNTAX]: "Use modulo before '{{0}}'."
};
function Ia(e, t, ...n) {
  const r = In(Ta[e], ...n || []), l = { message: String(r), code: e };
  return t && (l.location = t), l;
}
const D = {
  // tokenizer error codes
  EXPECTED_TOKEN: 1,
  INVALID_TOKEN_IN_PLACEHOLDER: 2,
  UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER: 3,
  UNKNOWN_ESCAPE_SEQUENCE: 4,
  INVALID_UNICODE_ESCAPE_SEQUENCE: 5,
  UNBALANCED_CLOSING_BRACE: 6,
  UNTERMINATED_CLOSING_BRACE: 7,
  EMPTY_PLACEHOLDER: 8,
  NOT_ALLOW_NEST_PLACEHOLDER: 9,
  INVALID_LINKED_FORMAT: 10,
  // parser error codes
  MUST_HAVE_MESSAGES_IN_PLURAL: 11,
  UNEXPECTED_EMPTY_LINKED_MODIFIER: 12,
  UNEXPECTED_EMPTY_LINKED_KEY: 13,
  UNEXPECTED_LEXICAL_ANALYSIS: 14,
  // generator error codes
  UNHANDLED_CODEGEN_NODE_TYPE: 15,
  // minifier error codes
  UNHANDLED_MINIFIER_NODE_TYPE: 16,
  // Special value for higher-order compilers to pick up the last code
  // to avoid collision of error codes. This should always be kept as the last
  // item.
  __EXTEND_POINT__: 17
}, ba = {
  // tokenizer error messages
  [D.EXPECTED_TOKEN]: "Expected token: '{0}'",
  [D.INVALID_TOKEN_IN_PLACEHOLDER]: "Invalid token in placeholder: '{0}'",
  [D.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER]: "Unterminated single quote in placeholder",
  [D.UNKNOWN_ESCAPE_SEQUENCE]: "Unknown escape sequence: \\{0}",
  [D.INVALID_UNICODE_ESCAPE_SEQUENCE]: "Invalid unicode escape sequence: {0}",
  [D.UNBALANCED_CLOSING_BRACE]: "Unbalanced closing brace",
  [D.UNTERMINATED_CLOSING_BRACE]: "Unterminated closing brace",
  [D.EMPTY_PLACEHOLDER]: "Empty placeholder",
  [D.NOT_ALLOW_NEST_PLACEHOLDER]: "Not allowed nest placeholder",
  [D.INVALID_LINKED_FORMAT]: "Invalid linked format",
  // parser error messages
  [D.MUST_HAVE_MESSAGES_IN_PLURAL]: "Plural must have messages",
  [D.UNEXPECTED_EMPTY_LINKED_MODIFIER]: "Unexpected empty linked modifier",
  [D.UNEXPECTED_EMPTY_LINKED_KEY]: "Unexpected empty linked key",
  [D.UNEXPECTED_LEXICAL_ANALYSIS]: "Unexpected lexical analysis in token: '{0}'",
  // generator error messages
  [D.UNHANDLED_CODEGEN_NODE_TYPE]: "unhandled codegen node type: '{0}'",
  // minimizer error messages
  [D.UNHANDLED_MINIFIER_NODE_TYPE]: "unhandled mimifier node type: '{0}'"
};
function De(e, t, n = {}) {
  const { domain: r, messages: l, args: a } = n, s = In((l || ba)[e] || "", ...a || []), i = new SyntaxError(String(s));
  return i.code = e, t && (i.location = t), i.domain = r, i;
}
function Oa(e) {
  throw e;
}
const fe = " ", ha = "\r", z = `
`, pa = "\u2028", va = "\u2029";
function Ca(e) {
  const t = e;
  let n = 0, r = 1, l = 1, a = 0;
  const s = (h) => t[h] === ha && t[h + 1] === z, i = (h) => t[h] === z, u = (h) => t[h] === va, _ = (h) => t[h] === pa, I = (h) => s(h) || i(h) || u(h) || _(h), T = () => n, g = () => r, v = () => l, S = () => a, P = (h) => s(h) || u(h) || _(h) ? z : t[h], k = () => P(n), y = () => P(n + a);
  function U() {
    return a = 0, I(n) && (r++, l = 0), s(n) && n++, n++, l++, t[n];
  }
  function m() {
    return s(n + a) && a++, a++, t[n + a];
  }
  function d() {
    n = 0, r = 1, l = 1, a = 0;
  }
  function b(h = 0) {
    a = h;
  }
  function L() {
    const h = n + a;
    for (; h !== n; )
      U();
    a = 0;
  }
  return {
    index: T,
    line: g,
    column: v,
    peekOffset: S,
    charAt: P,
    currentChar: k,
    currentPeek: y,
    next: U,
    peek: m,
    reset: d,
    resetPeek: b,
    skipToPeek: L
  };
}
const Ee = void 0, Pa = ".", wt = "'", Aa = "tokenizer";
function ka(e, t = {}) {
  const n = t.location !== !1, r = Ca(e), l = () => r.index(), a = () => ga(r.line(), r.column(), r.index()), s = a(), i = l(), u = {
    currentType: 14,
    offset: i,
    startLoc: s,
    endLoc: s,
    lastType: 14,
    lastOffset: i,
    lastStartLoc: s,
    lastEndLoc: s,
    braceNest: 0,
    inLinked: !1,
    text: ""
  }, _ = () => u, { onError: I } = t;
  function T(c, o, E, ...R) {
    const X = _();
    if (o.column += E, o.offset += E, I) {
      const M = n ? st(X.startLoc, o) : null, f = De(c, M, {
        domain: Aa,
        args: R
      });
      I(f);
    }
  }
  function g(c, o, E) {
    c.endLoc = a(), c.currentType = o;
    const R = { type: o };
    return n && (R.loc = st(c.startLoc, c.endLoc)), E != null && (R.value = E), R;
  }
  const v = (c) => g(
    c,
    14
    /* TokenTypes.EOF */
  );
  function S(c, o) {
    return c.currentChar() === o ? (c.next(), o) : (T(D.EXPECTED_TOKEN, a(), 0, o), "");
  }
  function P(c) {
    let o = "";
    for (; c.currentPeek() === fe || c.currentPeek() === z; )
      o += c.currentPeek(), c.peek();
    return o;
  }
  function k(c) {
    const o = P(c);
    return c.skipToPeek(), o;
  }
  function y(c) {
    if (c === Ee)
      return !1;
    const o = c.charCodeAt(0);
    return o >= 97 && o <= 122 || // a-z
    o >= 65 && o <= 90 || // A-Z
    o === 95;
  }
  function U(c) {
    if (c === Ee)
      return !1;
    const o = c.charCodeAt(0);
    return o >= 48 && o <= 57;
  }
  function m(c, o) {
    const { currentType: E } = o;
    if (E !== 2)
      return !1;
    P(c);
    const R = y(c.currentPeek());
    return c.resetPeek(), R;
  }
  function d(c, o) {
    const { currentType: E } = o;
    if (E !== 2)
      return !1;
    P(c);
    const R = c.currentPeek() === "-" ? c.peek() : c.currentPeek(), X = U(R);
    return c.resetPeek(), X;
  }
  function b(c, o) {
    const { currentType: E } = o;
    if (E !== 2)
      return !1;
    P(c);
    const R = c.currentPeek() === wt;
    return c.resetPeek(), R;
  }
  function L(c, o) {
    const { currentType: E } = o;
    if (E !== 8)
      return !1;
    P(c);
    const R = c.currentPeek() === ".";
    return c.resetPeek(), R;
  }
  function h(c, o) {
    const { currentType: E } = o;
    if (E !== 9)
      return !1;
    P(c);
    const R = y(c.currentPeek());
    return c.resetPeek(), R;
  }
  function A(c, o) {
    const { currentType: E } = o;
    if (!(E === 8 || E === 12))
      return !1;
    P(c);
    const R = c.currentPeek() === ":";
    return c.resetPeek(), R;
  }
  function C(c, o) {
    const { currentType: E } = o;
    if (E !== 10)
      return !1;
    const R = () => {
      const M = c.currentPeek();
      return M === "{" ? y(c.peek()) : M === "@" || M === "%" || M === "|" || M === ":" || M === "." || M === fe || !M ? !1 : M === z ? (c.peek(), R()) : W(c, !1);
    }, X = R();
    return c.resetPeek(), X;
  }
  function B(c) {
    P(c);
    const o = c.currentPeek() === "|";
    return c.resetPeek(), o;
  }
  function te(c) {
    const o = P(c), E = c.currentPeek() === "%" && c.peek() === "{";
    return c.resetPeek(), {
      isModulo: E,
      hasSpace: o.length > 0
    };
  }
  function W(c, o = !0) {
    const E = (X = !1, M = "", f = !1) => {
      const N = c.currentPeek();
      return N === "{" ? M === "%" ? !1 : X : N === "@" || !N ? M === "%" ? !0 : X : N === "%" ? (c.peek(), E(X, "%", !0)) : N === "|" ? M === "%" || f ? !0 : !(M === fe || M === z) : N === fe ? (c.peek(), E(!0, fe, f)) : N === z ? (c.peek(), E(!0, z, f)) : !0;
    }, R = E();
    return o && c.resetPeek(), R;
  }
  function Z(c, o) {
    const E = c.currentChar();
    return E === Ee ? Ee : o(E) ? (c.next(), E) : null;
  }
  function Fe(c) {
    const o = c.charCodeAt(0);
    return o >= 97 && o <= 122 || // a-z
    o >= 65 && o <= 90 || // A-Z
    o >= 48 && o <= 57 || // 0-9
    o === 95 || // _
    o === 36;
  }
  function Me(c) {
    return Z(c, Fe);
  }
  function ce(c) {
    const o = c.charCodeAt(0);
    return o >= 97 && o <= 122 || // a-z
    o >= 65 && o <= 90 || // A-Z
    o >= 48 && o <= 57 || // 0-9
    o === 95 || // _
    o === 36 || // $
    o === 45;
  }
  function Ue(c) {
    return Z(c, ce);
  }
  function we(c) {
    const o = c.charCodeAt(0);
    return o >= 48 && o <= 57;
  }
  function We(c) {
    return Z(c, we);
  }
  function $e(c) {
    const o = c.charCodeAt(0);
    return o >= 48 && o <= 57 || // 0-9
    o >= 65 && o <= 70 || // A-F
    o >= 97 && o <= 102;
  }
  function ae(c) {
    return Z(c, $e);
  }
  function Ie(c) {
    let o = "", E = "";
    for (; o = We(c); )
      E += o;
    return E;
  }
  function Ve(c) {
    k(c);
    const o = c.currentChar();
    return o !== "%" && T(D.EXPECTED_TOKEN, a(), 0, o), c.next(), "%";
  }
  function he(c) {
    let o = "";
    for (; ; ) {
      const E = c.currentChar();
      if (E === "{" || E === "}" || E === "@" || E === "|" || !E)
        break;
      if (E === "%")
        if (W(c))
          o += E, c.next();
        else
          break;
      else if (E === fe || E === z)
        if (W(c))
          o += E, c.next();
        else {
          if (B(c))
            break;
          o += E, c.next();
        }
      else
        o += E, c.next();
    }
    return o;
  }
  function He(c) {
    k(c);
    let o = "", E = "";
    for (; o = Ue(c); )
      E += o;
    return c.currentChar() === Ee && T(D.UNTERMINATED_CLOSING_BRACE, a(), 0), E;
  }
  function Ye(c) {
    k(c);
    let o = "";
    return c.currentChar() === "-" ? (c.next(), o += `-${Ie(c)}`) : o += Ie(c), c.currentChar() === Ee && T(D.UNTERMINATED_CLOSING_BRACE, a(), 0), o;
  }
  function et(c) {
    return c !== wt && c !== z;
  }
  function Ge(c) {
    k(c), S(c, "'");
    let o = "", E = "";
    for (; o = Z(c, et); )
      o === "\\" ? E += xe(c) : E += o;
    const R = c.currentChar();
    return R === z || R === Ee ? (T(D.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER, a(), 0), R === z && (c.next(), S(c, "'")), E) : (S(c, "'"), E);
  }
  function xe(c) {
    const o = c.currentChar();
    switch (o) {
      case "\\":
      case "'":
        return c.next(), `\\${o}`;
      case "u":
        return pe(c, o, 4);
      case "U":
        return pe(c, o, 6);
      default:
        return T(D.UNKNOWN_ESCAPE_SEQUENCE, a(), 0, o), "";
    }
  }
  function pe(c, o, E) {
    S(c, o);
    let R = "";
    for (let X = 0; X < E; X++) {
      const M = ae(c);
      if (!M) {
        T(D.INVALID_UNICODE_ESCAPE_SEQUENCE, a(), 0, `\\${o}${R}${c.currentChar()}`);
        break;
      }
      R += M;
    }
    return `\\${o}${R}`;
  }
  function Xe(c) {
    return c !== "{" && c !== "}" && c !== fe && c !== z;
  }
  function Ke(c) {
    k(c);
    let o = "", E = "";
    for (; o = Z(c, Xe); )
      E += o;
    return E;
  }
  function je(c) {
    let o = "", E = "";
    for (; o = Me(c); )
      E += o;
    return E;
  }
  function O(c) {
    const o = (E) => {
      const R = c.currentChar();
      return R === "{" || R === "%" || R === "@" || R === "|" || R === "(" || R === ")" || !R || R === fe ? E : (E += R, c.next(), o(E));
    };
    return o("");
  }
  function V(c) {
    k(c);
    const o = S(
      c,
      "|"
      /* TokenChars.Pipe */
    );
    return k(c), o;
  }
  function ve(c, o) {
    let E = null;
    switch (c.currentChar()) {
      case "{":
        return o.braceNest >= 1 && T(D.NOT_ALLOW_NEST_PLACEHOLDER, a(), 0), c.next(), E = g(
          o,
          2,
          "{"
          /* TokenChars.BraceLeft */
        ), k(c), o.braceNest++, E;
      case "}":
        return o.braceNest > 0 && o.currentType === 2 && T(D.EMPTY_PLACEHOLDER, a(), 0), c.next(), E = g(
          o,
          3,
          "}"
          /* TokenChars.BraceRight */
        ), o.braceNest--, o.braceNest > 0 && k(c), o.inLinked && o.braceNest === 0 && (o.inLinked = !1), E;
      case "@":
        return o.braceNest > 0 && T(D.UNTERMINATED_CLOSING_BRACE, a(), 0), E = Ce(c, o) || v(o), o.braceNest = 0, E;
      default: {
        let X = !0, M = !0, f = !0;
        if (B(c))
          return o.braceNest > 0 && T(D.UNTERMINATED_CLOSING_BRACE, a(), 0), E = g(o, 1, V(c)), o.braceNest = 0, o.inLinked = !1, E;
        if (o.braceNest > 0 && (o.currentType === 5 || o.currentType === 6 || o.currentType === 7))
          return T(D.UNTERMINATED_CLOSING_BRACE, a(), 0), o.braceNest = 0, Be(c, o);
        if (X = m(c, o))
          return E = g(o, 5, He(c)), k(c), E;
        if (M = d(c, o))
          return E = g(o, 6, Ye(c)), k(c), E;
        if (f = b(c, o))
          return E = g(o, 7, Ge(c)), k(c), E;
        if (!X && !M && !f)
          return E = g(o, 13, Ke(c)), T(D.INVALID_TOKEN_IN_PLACEHOLDER, a(), 0, E.value), k(c), E;
        break;
      }
    }
    return E;
  }
  function Ce(c, o) {
    const { currentType: E } = o;
    let R = null;
    const X = c.currentChar();
    switch ((E === 8 || E === 9 || E === 12 || E === 10) && (X === z || X === fe) && T(D.INVALID_LINKED_FORMAT, a(), 0), X) {
      case "@":
        return c.next(), R = g(
          o,
          8,
          "@"
          /* TokenChars.LinkedAlias */
        ), o.inLinked = !0, R;
      case ".":
        return k(c), c.next(), g(
          o,
          9,
          "."
          /* TokenChars.LinkedDot */
        );
      case ":":
        return k(c), c.next(), g(
          o,
          10,
          ":"
          /* TokenChars.LinkedDelimiter */
        );
      default:
        return B(c) ? (R = g(o, 1, V(c)), o.braceNest = 0, o.inLinked = !1, R) : L(c, o) || A(c, o) ? (k(c), Ce(c, o)) : h(c, o) ? (k(c), g(o, 12, je(c))) : C(c, o) ? (k(c), X === "{" ? ve(c, o) || R : g(o, 11, O(c))) : (E === 8 && T(D.INVALID_LINKED_FORMAT, a(), 0), o.braceNest = 0, o.inLinked = !1, Be(c, o));
    }
  }
  function Be(c, o) {
    let E = {
      type: 14
      /* TokenTypes.EOF */
    };
    if (o.braceNest > 0)
      return ve(c, o) || v(o);
    if (o.inLinked)
      return Ce(c, o) || v(o);
    switch (c.currentChar()) {
      case "{":
        return ve(c, o) || v(o);
      case "}":
        return T(D.UNBALANCED_CLOSING_BRACE, a(), 0), c.next(), g(
          o,
          3,
          "}"
          /* TokenChars.BraceRight */
        );
      case "@":
        return Ce(c, o) || v(o);
      default: {
        if (B(c))
          return E = g(o, 1, V(c)), o.braceNest = 0, o.inLinked = !1, E;
        const { isModulo: X, hasSpace: M } = te(c);
        if (X)
          return M ? g(o, 0, he(c)) : g(o, 4, Ve(c));
        if (W(c))
          return g(o, 0, he(c));
        break;
      }
    }
    return E;
  }
  function ft() {
    const { currentType: c, offset: o, startLoc: E, endLoc: R } = u;
    return u.lastType = c, u.lastOffset = o, u.lastStartLoc = E, u.lastEndLoc = R, u.offset = l(), u.startLoc = a(), r.currentChar() === Ee ? g(
      u,
      14
      /* TokenTypes.EOF */
    ) : Be(r, u);
  }
  return {
    nextToken: ft,
    currentOffset: l,
    currentPosition: a,
    context: _
  };
}
const ya = "parser", Sa = /(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;
function Ra(e, t, n) {
  switch (e) {
    case "\\\\":
      return "\\";
    // eslint-disable-next-line no-useless-escape
    case "\\'":
      return "'";
    default: {
      const r = parseInt(t || n, 16);
      return r <= 55295 || r >= 57344 ? String.fromCodePoint(r) : "�";
    }
  }
}
function Da(e = {}) {
  const t = e.location !== !1, { onError: n, onWarn: r } = e;
  function l(m, d, b, L, ...h) {
    const A = m.currentPosition();
    if (A.offset += L, A.column += L, n) {
      const C = t ? st(b, A) : null, B = De(d, C, {
        domain: ya,
        args: h
      });
      n(B);
    }
  }
  function a(m, d, b, L, ...h) {
    const A = m.currentPosition();
    if (A.offset += L, A.column += L, r) {
      const C = t ? st(b, A) : null;
      r(Ia(d, C, h));
    }
  }
  function s(m, d, b) {
    const L = { type: m };
    return t && (L.start = d, L.end = d, L.loc = { start: b, end: b }), L;
  }
  function i(m, d, b, L) {
    t && (m.end = d, m.loc && (m.loc.end = b));
  }
  function u(m, d) {
    const b = m.context(), L = s(3, b.offset, b.startLoc);
    return L.value = d, i(L, m.currentOffset(), m.currentPosition()), L;
  }
  function _(m, d) {
    const b = m.context(), { lastOffset: L, lastStartLoc: h } = b, A = s(5, L, h);
    return A.index = parseInt(d, 10), m.nextToken(), i(A, m.currentOffset(), m.currentPosition()), A;
  }
  function I(m, d, b) {
    const L = m.context(), { lastOffset: h, lastStartLoc: A } = L, C = s(4, h, A);
    return C.key = d, b === !0 && (C.modulo = !0), m.nextToken(), i(C, m.currentOffset(), m.currentPosition()), C;
  }
  function T(m, d) {
    const b = m.context(), { lastOffset: L, lastStartLoc: h } = b, A = s(9, L, h);
    return A.value = d.replace(Sa, Ra), m.nextToken(), i(A, m.currentOffset(), m.currentPosition()), A;
  }
  function g(m) {
    const d = m.nextToken(), b = m.context(), { lastOffset: L, lastStartLoc: h } = b, A = s(8, L, h);
    return d.type !== 12 ? (l(m, D.UNEXPECTED_EMPTY_LINKED_MODIFIER, b.lastStartLoc, 0), A.value = "", i(A, L, h), {
      nextConsumeToken: d,
      node: A
    }) : (d.value == null && l(m, D.UNEXPECTED_LEXICAL_ANALYSIS, b.lastStartLoc, 0, re(d)), A.value = d.value || "", i(A, m.currentOffset(), m.currentPosition()), {
      node: A
    });
  }
  function v(m, d) {
    const b = m.context(), L = s(7, b.offset, b.startLoc);
    return L.value = d, i(L, m.currentOffset(), m.currentPosition()), L;
  }
  function S(m) {
    const d = m.context(), b = s(6, d.offset, d.startLoc);
    let L = m.nextToken();
    if (L.type === 9) {
      const h = g(m);
      b.modifier = h.node, L = h.nextConsumeToken || m.nextToken();
    }
    switch (L.type !== 10 && l(m, D.UNEXPECTED_LEXICAL_ANALYSIS, d.lastStartLoc, 0, re(L)), L = m.nextToken(), L.type === 2 && (L = m.nextToken()), L.type) {
      case 11:
        L.value == null && l(m, D.UNEXPECTED_LEXICAL_ANALYSIS, d.lastStartLoc, 0, re(L)), b.key = v(m, L.value || "");
        break;
      case 5:
        L.value == null && l(m, D.UNEXPECTED_LEXICAL_ANALYSIS, d.lastStartLoc, 0, re(L)), b.key = I(m, L.value || "");
        break;
      case 6:
        L.value == null && l(m, D.UNEXPECTED_LEXICAL_ANALYSIS, d.lastStartLoc, 0, re(L)), b.key = _(m, L.value || "");
        break;
      case 7:
        L.value == null && l(m, D.UNEXPECTED_LEXICAL_ANALYSIS, d.lastStartLoc, 0, re(L)), b.key = T(m, L.value || "");
        break;
      default: {
        l(m, D.UNEXPECTED_EMPTY_LINKED_KEY, d.lastStartLoc, 0);
        const h = m.context(), A = s(7, h.offset, h.startLoc);
        return A.value = "", i(A, h.offset, h.startLoc), b.key = A, i(b, h.offset, h.startLoc), {
          nextConsumeToken: L,
          node: b
        };
      }
    }
    return i(b, m.currentOffset(), m.currentPosition()), {
      node: b
    };
  }
  function P(m) {
    const d = m.context(), b = d.currentType === 1 ? m.currentOffset() : d.offset, L = d.currentType === 1 ? d.endLoc : d.startLoc, h = s(2, b, L);
    h.items = [];
    let A = null, C = null;
    do {
      const W = A || m.nextToken();
      switch (A = null, W.type) {
        case 0:
          W.value == null && l(m, D.UNEXPECTED_LEXICAL_ANALYSIS, d.lastStartLoc, 0, re(W)), h.items.push(u(m, W.value || ""));
          break;
        case 6:
          W.value == null && l(m, D.UNEXPECTED_LEXICAL_ANALYSIS, d.lastStartLoc, 0, re(W)), h.items.push(_(m, W.value || ""));
          break;
        case 4:
          C = !0;
          break;
        case 5:
          W.value == null && l(m, D.UNEXPECTED_LEXICAL_ANALYSIS, d.lastStartLoc, 0, re(W)), h.items.push(I(m, W.value || "", !!C)), C && (a(m, Ct.USE_MODULO_SYNTAX, d.lastStartLoc, 0, re(W)), C = null);
          break;
        case 7:
          W.value == null && l(m, D.UNEXPECTED_LEXICAL_ANALYSIS, d.lastStartLoc, 0, re(W)), h.items.push(T(m, W.value || ""));
          break;
        case 8: {
          const Z = S(m);
          h.items.push(Z.node), A = Z.nextConsumeToken || null;
          break;
        }
      }
    } while (d.currentType !== 14 && d.currentType !== 1);
    const B = d.currentType === 1 ? d.lastOffset : m.currentOffset(), te = d.currentType === 1 ? d.lastEndLoc : m.currentPosition();
    return i(h, B, te), h;
  }
  function k(m, d, b, L) {
    const h = m.context();
    let A = L.items.length === 0;
    const C = s(1, d, b);
    C.cases = [], C.cases.push(L);
    do {
      const B = P(m);
      A || (A = B.items.length === 0), C.cases.push(B);
    } while (h.currentType !== 14);
    return A && l(m, D.MUST_HAVE_MESSAGES_IN_PLURAL, b, 0), i(C, m.currentOffset(), m.currentPosition()), C;
  }
  function y(m) {
    const d = m.context(), { offset: b, startLoc: L } = d, h = P(m);
    return d.currentType === 14 ? h : k(m, b, L, h);
  }
  function U(m) {
    const d = ka(m, bn({}, e)), b = d.context(), L = s(0, b.offset, b.startLoc);
    return t && L.loc && (L.loc.source = m), L.body = y(d), e.onCacheKey && (L.cacheKey = e.onCacheKey(m)), b.currentType !== 14 && l(d, D.UNEXPECTED_LEXICAL_ANALYSIS, b.lastStartLoc, 0, m[b.offset] || ""), i(L, d.currentOffset(), d.currentPosition()), L;
  }
  return { parse: U };
}
function re(e) {
  if (e.type === 14)
    return "EOF";
  const t = (e.value || "").replace(/\r?\n/gu, "\\n");
  return t.length > 10 ? t.slice(0, 9) + "…" : t;
}
function Fa(e, t = {}) {
  const n = {
    ast: e,
    helpers: /* @__PURE__ */ new Set()
  };
  return { context: () => n, helper: (a) => (n.helpers.add(a), a) };
}
function Wt(e, t) {
  for (let n = 0; n < e.length; n++)
    Pt(e[n], t);
}
function Pt(e, t) {
  switch (e.type) {
    case 1:
      Wt(e.cases, t), t.helper(
        "plural"
        /* HelperNameMap.PLURAL */
      );
      break;
    case 2:
      Wt(e.items, t);
      break;
    case 6: {
      Pt(e.key, t), t.helper(
        "linked"
        /* HelperNameMap.LINKED */
      ), t.helper(
        "type"
        /* HelperNameMap.TYPE */
      );
      break;
    }
    case 5:
      t.helper(
        "interpolate"
        /* HelperNameMap.INTERPOLATE */
      ), t.helper(
        "list"
        /* HelperNameMap.LIST */
      );
      break;
    case 4:
      t.helper(
        "interpolate"
        /* HelperNameMap.INTERPOLATE */
      ), t.helper(
        "named"
        /* HelperNameMap.NAMED */
      );
      break;
  }
}
function Ma(e, t = {}) {
  const n = Fa(e);
  n.helper(
    "normalize"
    /* HelperNameMap.NORMALIZE */
  ), e.body && Pt(e.body, n);
  const r = n.context();
  e.helpers = Array.from(r.helpers);
}
function Ua(e) {
  const t = e.body;
  return t.type === 2 ? $t(t) : t.cases.forEach((n) => $t(n)), e;
}
function $t(e) {
  if (e.items.length === 1) {
    const t = e.items[0];
    (t.type === 3 || t.type === 9) && (e.static = t.value, delete t.value);
  } else {
    const t = [];
    for (let n = 0; n < e.items.length; n++) {
      const r = e.items[n];
      if (!(r.type === 3 || r.type === 9) || r.value == null)
        break;
      t.push(r.value);
    }
    if (t.length === e.items.length) {
      e.static = On(t);
      for (let n = 0; n < e.items.length; n++) {
        const r = e.items[n];
        (r.type === 3 || r.type === 9) && delete r.value;
      }
    }
  }
}
const wa = "minifier";
function ke(e) {
  switch (e.t = e.type, e.type) {
    case 0: {
      const t = e;
      ke(t.body), t.b = t.body, delete t.body;
      break;
    }
    case 1: {
      const t = e, n = t.cases;
      for (let r = 0; r < n.length; r++)
        ke(n[r]);
      t.c = n, delete t.cases;
      break;
    }
    case 2: {
      const t = e, n = t.items;
      for (let r = 0; r < n.length; r++)
        ke(n[r]);
      t.i = n, delete t.items, t.static && (t.s = t.static, delete t.static);
      break;
    }
    case 3:
    case 9:
    case 8:
    case 7: {
      const t = e;
      t.value && (t.v = t.value, delete t.value);
      break;
    }
    case 6: {
      const t = e;
      ke(t.key), t.k = t.key, delete t.key, t.modifier && (ke(t.modifier), t.m = t.modifier, delete t.modifier);
      break;
    }
    case 5: {
      const t = e;
      t.i = t.index, delete t.index;
      break;
    }
    case 4: {
      const t = e;
      t.k = t.key, delete t.key;
      break;
    }
    default:
      throw De(D.UNHANDLED_MINIFIER_NODE_TYPE, null, {
        domain: wa,
        args: [e.type]
      });
  }
  delete e.type;
}
const Wa = "parser";
function $a(e, t) {
  const { filename: n, breakLineCode: r, needIndent: l } = t, a = t.location !== !1, s = {
    filename: n,
    code: "",
    column: 1,
    line: 1,
    offset: 0,
    map: void 0,
    breakLineCode: r,
    needIndent: l,
    indentLevel: 0
  };
  a && e.loc && (s.source = e.loc.source);
  const i = () => s;
  function u(P, k) {
    s.code += P;
  }
  function _(P, k = !0) {
    const y = k ? r : "";
    u(l ? y + "  ".repeat(P) : y);
  }
  function I(P = !0) {
    const k = ++s.indentLevel;
    P && _(k);
  }
  function T(P = !0) {
    const k = --s.indentLevel;
    P && _(k);
  }
  function g() {
    _(s.indentLevel);
  }
  return {
    context: i,
    push: u,
    indent: I,
    deindent: T,
    newline: g,
    helper: (P) => `_${P}`,
    needIndent: () => s.needIndent
  };
}
function Va(e, t) {
  const { helper: n } = e;
  e.push(`${n(
    "linked"
    /* HelperNameMap.LINKED */
  )}(`), Se(e, t.key), t.modifier ? (e.push(", "), Se(e, t.modifier), e.push(", _type")) : e.push(", undefined, _type"), e.push(")");
}
function Ha(e, t) {
  const { helper: n, needIndent: r } = e;
  e.push(`${n(
    "normalize"
    /* HelperNameMap.NORMALIZE */
  )}([`), e.indent(r());
  const l = t.items.length;
  for (let a = 0; a < l && (Se(e, t.items[a]), a !== l - 1); a++)
    e.push(", ");
  e.deindent(r()), e.push("])");
}
function Ya(e, t) {
  const { helper: n, needIndent: r } = e;
  if (t.cases.length > 1) {
    e.push(`${n(
      "plural"
      /* HelperNameMap.PLURAL */
    )}([`), e.indent(r());
    const l = t.cases.length;
    for (let a = 0; a < l && (Se(e, t.cases[a]), a !== l - 1); a++)
      e.push(", ");
    e.deindent(r()), e.push("])");
  }
}
function Ga(e, t) {
  t.body ? Se(e, t.body) : e.push("null");
}
function Se(e, t) {
  const { helper: n } = e;
  switch (t.type) {
    case 0:
      Ga(e, t);
      break;
    case 1:
      Ya(e, t);
      break;
    case 2:
      Ha(e, t);
      break;
    case 6:
      Va(e, t);
      break;
    case 8:
      e.push(JSON.stringify(t.value), t);
      break;
    case 7:
      e.push(JSON.stringify(t.value), t);
      break;
    case 5:
      e.push(`${n(
        "interpolate"
        /* HelperNameMap.INTERPOLATE */
      )}(${n(
        "list"
        /* HelperNameMap.LIST */
      )}(${t.index}))`, t);
      break;
    case 4:
      e.push(`${n(
        "interpolate"
        /* HelperNameMap.INTERPOLATE */
      )}(${n(
        "named"
        /* HelperNameMap.NAMED */
      )}(${JSON.stringify(t.key)}))`, t);
      break;
    case 9:
      e.push(JSON.stringify(t.value), t);
      break;
    case 3:
      e.push(JSON.stringify(t.value), t);
      break;
    default:
      throw De(D.UNHANDLED_CODEGEN_NODE_TYPE, null, {
        domain: Wa,
        args: [t.type]
      });
  }
}
const xa = (e, t = {}) => {
  const n = Ut(t.mode) ? t.mode : "normal", r = Ut(t.filename) ? t.filename : "message.intl";
  t.sourceMap;
  const l = t.breakLineCode != null ? t.breakLineCode : n === "arrow" ? ";" : `
`, a = t.needIndent ? t.needIndent : n !== "arrow", s = e.helpers || [], i = $a(e, {
    filename: r,
    breakLineCode: l,
    needIndent: a
  });
  i.push(n === "normal" ? "function __msg__ (ctx) {" : "(ctx) => {"), i.indent(a), s.length > 0 && (i.push(`const { ${On(s.map((I) => `${I}: _${I}`), ", ")} } = ctx`), i.newline()), i.push("return "), Se(i, e), i.deindent(a), i.push("}"), delete e.helpers;
  const { code: u, map: _ } = i.context();
  return {
    ast: e,
    code: u,
    map: _ ? _.toJSON() : void 0
    // eslint-disable-line @typescript-eslint/no-explicit-any
  };
};
function Xa(e, t = {}) {
  const n = bn({}, t), r = !!n.jit, l = !!n.minify, a = n.optimize == null ? !0 : n.optimize, i = Da(n).parse(e);
  return r ? (a && Ua(i), l && ke(i), { ast: i, code: "" }) : (Ma(i, n), xa(i, n));
}
/*!
  * core-base v9.14.4
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */
function Ka() {
  typeof __INTLIFY_PROD_DEVTOOLS__ != "boolean" && (me().__INTLIFY_PROD_DEVTOOLS__ = !1), typeof __INTLIFY_JIT_COMPILATION__ != "boolean" && (me().__INTLIFY_JIT_COMPILATION__ = !1), typeof __INTLIFY_DROP_MESSAGE_COMPILER__ != "boolean" && (me().__INTLIFY_DROP_MESSAGE_COMPILER__ = !1);
}
function ue(e) {
  return $(e) && At(e) === 0 && (le(e, "b") || le(e, "body"));
}
const hn = ["b", "body"];
function ja(e) {
  return Ne(e, hn);
}
const pn = ["c", "cases"];
function Ba(e) {
  return Ne(e, pn, []);
}
const vn = ["s", "static"];
function Ja(e) {
  return Ne(e, vn);
}
const Cn = ["i", "items"];
function Qa(e) {
  return Ne(e, Cn, []);
}
const Pn = ["t", "type"];
function At(e) {
  return Ne(e, Pn);
}
const An = ["v", "value"];
function at(e, t) {
  const n = Ne(e, An);
  if (n != null)
    return n;
  throw qe(t);
}
const kn = ["m", "modifier"];
function qa(e) {
  return Ne(e, kn);
}
const yn = ["k", "key"];
function Za(e) {
  const t = Ne(e, yn);
  if (t)
    return t;
  throw qe(
    6
    /* NodeTypes.Linked */
  );
}
function Ne(e, t, n) {
  for (let r = 0; r < t.length; r++) {
    const l = t[r];
    if (le(e, l) && e[l] != null)
      return e[l];
  }
  return n;
}
const Sn = [
  ...hn,
  ...pn,
  ...vn,
  ...Cn,
  ...yn,
  ...kn,
  ...An,
  ...Pn
];
function qe(e) {
  return new Error(`unhandled node type: ${e}`);
}
const Te = [];
Te[
  0
  /* States.BEFORE_PATH */
] = {
  w: [
    0
    /* States.BEFORE_PATH */
  ],
  i: [
    3,
    0
    /* Actions.APPEND */
  ],
  "[": [
    4
    /* States.IN_SUB_PATH */
  ],
  o: [
    7
    /* States.AFTER_PATH */
  ]
};
Te[
  1
  /* States.IN_PATH */
] = {
  w: [
    1
    /* States.IN_PATH */
  ],
  ".": [
    2
    /* States.BEFORE_IDENT */
  ],
  "[": [
    4
    /* States.IN_SUB_PATH */
  ],
  o: [
    7
    /* States.AFTER_PATH */
  ]
};
Te[
  2
  /* States.BEFORE_IDENT */
] = {
  w: [
    2
    /* States.BEFORE_IDENT */
  ],
  i: [
    3,
    0
    /* Actions.APPEND */
  ],
  0: [
    3,
    0
    /* Actions.APPEND */
  ]
};
Te[
  3
  /* States.IN_IDENT */
] = {
  i: [
    3,
    0
    /* Actions.APPEND */
  ],
  0: [
    3,
    0
    /* Actions.APPEND */
  ],
  w: [
    1,
    1
    /* Actions.PUSH */
  ],
  ".": [
    2,
    1
    /* Actions.PUSH */
  ],
  "[": [
    4,
    1
    /* Actions.PUSH */
  ],
  o: [
    7,
    1
    /* Actions.PUSH */
  ]
};
Te[
  4
  /* States.IN_SUB_PATH */
] = {
  "'": [
    5,
    0
    /* Actions.APPEND */
  ],
  '"': [
    6,
    0
    /* Actions.APPEND */
  ],
  "[": [
    4,
    2
    /* Actions.INC_SUB_PATH_DEPTH */
  ],
  "]": [
    1,
    3
    /* Actions.PUSH_SUB_PATH */
  ],
  o: 8,
  l: [
    4,
    0
    /* Actions.APPEND */
  ]
};
Te[
  5
  /* States.IN_SINGLE_QUOTE */
] = {
  "'": [
    4,
    0
    /* Actions.APPEND */
  ],
  o: 8,
  l: [
    5,
    0
    /* Actions.APPEND */
  ]
};
Te[
  6
  /* States.IN_DOUBLE_QUOTE */
] = {
  '"': [
    4,
    0
    /* Actions.APPEND */
  ],
  o: 8,
  l: [
    6,
    0
    /* Actions.APPEND */
  ]
};
const za = /^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;
function er(e) {
  return za.test(e);
}
function tr(e) {
  const t = e.charCodeAt(0), n = e.charCodeAt(e.length - 1);
  return t === n && (t === 34 || t === 39) ? e.slice(1, -1) : e;
}
function nr(e) {
  if (e == null)
    return "o";
  switch (e.charCodeAt(0)) {
    case 91:
    // [
    case 93:
    // ]
    case 46:
    // .
    case 34:
    // "
    case 39:
      return e;
    case 95:
    // _
    case 36:
    // $
    case 45:
      return "i";
    case 9:
    // Tab (HT)
    case 10:
    // Newline (LF)
    case 13:
    // Return (CR)
    case 160:
    // No-break space (NBSP)
    case 65279:
    // Byte Order Mark (BOM)
    case 8232:
    // Line Separator (LS)
    case 8233:
      return "w";
  }
  return "i";
}
function ar(e) {
  const t = e.trim();
  return e.charAt(0) === "0" && isNaN(parseInt(e)) ? !1 : er(t) ? tr(t) : "*" + t;
}
function rr(e) {
  const t = [];
  let n = -1, r = 0, l = 0, a, s, i, u, _, I, T;
  const g = [];
  g[
    0
    /* Actions.APPEND */
  ] = () => {
    s === void 0 ? s = i : s += i;
  }, g[
    1
    /* Actions.PUSH */
  ] = () => {
    s !== void 0 && (t.push(s), s = void 0);
  }, g[
    2
    /* Actions.INC_SUB_PATH_DEPTH */
  ] = () => {
    g[
      0
      /* Actions.APPEND */
    ](), l++;
  }, g[
    3
    /* Actions.PUSH_SUB_PATH */
  ] = () => {
    if (l > 0)
      l--, r = 4, g[
        0
        /* Actions.APPEND */
      ]();
    else {
      if (l = 0, s === void 0 || (s = ar(s), s === !1))
        return !1;
      g[
        1
        /* Actions.PUSH */
      ]();
    }
  };
  function v() {
    const S = e[n + 1];
    if (r === 5 && S === "'" || r === 6 && S === '"')
      return n++, i = "\\" + S, g[
        0
        /* Actions.APPEND */
      ](), !0;
  }
  for (; r !== null; )
    if (n++, a = e[n], !(a === "\\" && v())) {
      if (u = nr(a), T = Te[r], _ = T[u] || T.l || 8, _ === 8 || (r = _[0], _[1] !== void 0 && (I = g[_[1]], I && (i = a, I() === !1))))
        return;
      if (r === 7)
        return t;
    }
}
const Vt = /* @__PURE__ */ new Map();
function lr(e, t) {
  return $(e) ? e[t] : null;
}
function sr(e, t) {
  if (!$(e))
    return null;
  let n = Vt.get(t);
  if (n || (n = rr(t), n && Vt.set(t, n)), !n)
    return null;
  const r = n.length;
  let l = e, a = 0;
  for (; a < r; ) {
    const s = n[a];
    if (Sn.includes(s) && ue(l))
      return null;
    const i = l[s];
    if (i === void 0 || G(l))
      return null;
    l = i, a++;
  }
  return l;
}
const cr = (e) => e, or = (e) => "", ir = "text", ur = (e) => e.length === 0 ? "" : da(e), fr = ma;
function Ht(e, t) {
  return e = Math.abs(e), t === 2 ? e ? e > 1 ? 1 : 0 : 1 : e ? Math.min(e, 2) : 0;
}
function _r(e) {
  const t = j(e.pluralIndex) ? e.pluralIndex : -1;
  return e.named && (j(e.named.count) || j(e.named.n)) ? j(e.named.count) ? e.named.count : j(e.named.n) ? e.named.n : t : t;
}
function mr(e, t) {
  t.count || (t.count = e), t.n || (t.n = e);
}
function dr(e = {}) {
  const t = e.locale, n = _r(e), r = $(e.pluralRules) && p(t) && G(e.pluralRules[t]) ? e.pluralRules[t] : Ht, l = $(e.pluralRules) && p(t) && G(e.pluralRules[t]) ? Ht : void 0, a = (y) => y[r(n, y.length, l)], s = e.list || [], i = (y) => s[y], u = e.named || H();
  j(e.pluralIndex) && mr(n, u);
  const _ = (y) => u[y];
  function I(y) {
    const U = G(e.messages) ? e.messages(y) : $(e.messages) ? e.messages[y] : !1;
    return U || (e.parent ? e.parent.message(y) : or);
  }
  const T = (y) => e.modifiers ? e.modifiers[y] : cr, g = F(e.processor) && G(e.processor.normalize) ? e.processor.normalize : ur, v = F(e.processor) && G(e.processor.interpolate) ? e.processor.interpolate : fr, S = F(e.processor) && p(e.processor.type) ? e.processor.type : ir, k = {
    list: i,
    named: _,
    plural: a,
    linked: (y, ...U) => {
      const [m, d] = U;
      let b = "text", L = "";
      U.length === 1 ? $(m) ? (L = m.modifier || L, b = m.type || b) : p(m) && (L = m || L) : U.length === 2 && (p(m) && (L = m || L), p(d) && (b = d || b));
      const h = I(y)(k), A = (
        // The message in vnode resolved with linked are returned as an array by processor.nomalize
        b === "vnode" && x(h) && L ? h[0] : h
      );
      return L ? T(L)(A, b) : A;
    },
    message: I,
    type: S,
    interpolate: v,
    normalize: g,
    values: q(H(), s, u)
  };
  return k;
}
let Ze = null;
function Er(e) {
  Ze = e;
}
function gr(e, t, n) {
  Ze && Ze.emit("i18n:init", {
    timestamp: Date.now(),
    i18n: e,
    version: t,
    meta: n
  });
}
const Lr = /* @__PURE__ */ Nr(
  "function:translate"
  /* IntlifyDevToolsHooks.FunctionTranslate */
);
function Nr(e) {
  return (t) => Ze && Ze.emit(e, t);
}
const Tr = Ct.__EXTEND_POINT__, be = ot(Tr), Ir = {
  // 2
  FALLBACK_TO_TRANSLATE: be(),
  // 3
  CANNOT_FORMAT_NUMBER: be(),
  // 4
  FALLBACK_TO_NUMBER_FORMAT: be(),
  // 5
  CANNOT_FORMAT_DATE: be(),
  // 6
  FALLBACK_TO_DATE_FORMAT: be(),
  // 7
  EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER: be(),
  // 8
  __EXTEND_POINT__: be()
  // 9
}, Rn = D.__EXTEND_POINT__, Oe = ot(Rn), se = {
  INVALID_ARGUMENT: Rn,
  // 17
  INVALID_DATE_ARGUMENT: Oe(),
  // 18
  INVALID_ISO_DATE_ARGUMENT: Oe(),
  // 19
  NOT_SUPPORT_NON_STRING_MESSAGE: Oe(),
  // 20
  NOT_SUPPORT_LOCALE_PROMISE_VALUE: Oe(),
  // 21
  NOT_SUPPORT_LOCALE_ASYNC_FUNCTION: Oe(),
  // 22
  NOT_SUPPORT_LOCALE_TYPE: Oe(),
  // 23
  __EXTEND_POINT__: Oe()
  // 24
};
function ie(e) {
  return De(e, null, void 0);
}
function kt(e, t) {
  return t.locale != null ? Yt(t.locale) : Yt(e.locale);
}
let _t;
function Yt(e) {
  if (p(e))
    return e;
  if (G(e)) {
    if (e.resolvedOnce && _t != null)
      return _t;
    if (e.constructor.name === "Function") {
      const t = e();
      if (_a(t))
        throw ie(se.NOT_SUPPORT_LOCALE_PROMISE_VALUE);
      return _t = t;
    } else
      throw ie(se.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION);
  } else
    throw ie(se.NOT_SUPPORT_LOCALE_TYPE);
}
function br(e, t, n) {
  return [.../* @__PURE__ */ new Set([
    n,
    ...x(t) ? t : $(t) ? Object.keys(t) : p(t) ? [t] : [n]
  ])];
}
function Dn(e, t, n) {
  const r = p(n) ? n : Re, l = e;
  l.__localeChainCache || (l.__localeChainCache = /* @__PURE__ */ new Map());
  let a = l.__localeChainCache.get(r);
  if (!a) {
    a = [];
    let s = [n];
    for (; x(s); )
      s = Gt(a, s, t);
    const i = x(t) || !F(t) ? t : t.default ? t.default : null;
    s = p(i) ? [i] : i, x(s) && Gt(a, s, !1), l.__localeChainCache.set(r, a);
  }
  return a;
}
function Gt(e, t, n) {
  let r = !0;
  for (let l = 0; l < t.length && w(r); l++) {
    const a = t[l];
    p(a) && (r = Or(e, t[l], n));
  }
  return r;
}
function Or(e, t, n) {
  let r;
  const l = t.split("-");
  do {
    const a = l.join("-");
    r = hr(e, a, n), l.splice(-1, 1);
  } while (l.length && r === !0);
  return r;
}
function hr(e, t, n) {
  let r = !1;
  if (!e.includes(t) && (r = !0, t)) {
    r = t[t.length - 1] !== "!";
    const l = t.replace(/!/g, "");
    e.push(l), (x(n) || F(n)) && n[l] && (r = n[l]);
  }
  return r;
}
const pr = "9.14.4", it = -1, Re = "en-US", xt = "", Xt = (e) => `${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;
function vr() {
  return {
    upper: (e, t) => t === "text" && p(e) ? e.toUpperCase() : t === "vnode" && $(e) && "__v_isVNode" in e ? e.children.toUpperCase() : e,
    lower: (e, t) => t === "text" && p(e) ? e.toLowerCase() : t === "vnode" && $(e) && "__v_isVNode" in e ? e.children.toLowerCase() : e,
    capitalize: (e, t) => t === "text" && p(e) ? Xt(e) : t === "vnode" && $(e) && "__v_isVNode" in e ? Xt(e.children) : e
  };
}
let Fn;
function Kt(e) {
  Fn = e;
}
let Mn;
function Cr(e) {
  Mn = e;
}
let Un;
function Pr(e) {
  Un = e;
}
let wn = null;
const Ar = /* @__NO_SIDE_EFFECTS__ */ (e) => {
  wn = e;
}, kr = /* @__NO_SIDE_EFFECTS__ */ () => wn;
let Wn = null;
const jt = (e) => {
  Wn = e;
}, yr = () => Wn;
let Bt = 0;
function Sr(e = {}) {
  const t = G(e.onWarn) ? e.onWarn : Ea, n = p(e.version) ? e.version : pr, r = p(e.locale) || G(e.locale) ? e.locale : Re, l = G(r) ? Re : r, a = x(e.fallbackLocale) || F(e.fallbackLocale) || p(e.fallbackLocale) || e.fallbackLocale === !1 ? e.fallbackLocale : l, s = F(e.messages) ? e.messages : mt(l), i = F(e.datetimeFormats) ? e.datetimeFormats : mt(l), u = F(e.numberFormats) ? e.numberFormats : mt(l), _ = q(H(), e.modifiers, vr()), I = e.pluralRules || H(), T = G(e.missing) ? e.missing : null, g = w(e.missingWarn) || ge(e.missingWarn) ? e.missingWarn : !0, v = w(e.fallbackWarn) || ge(e.fallbackWarn) ? e.fallbackWarn : !0, S = !!e.fallbackFormat, P = !!e.unresolving, k = G(e.postTranslation) ? e.postTranslation : null, y = F(e.processor) ? e.processor : null, U = w(e.warnHtmlMessage) ? e.warnHtmlMessage : !0, m = !!e.escapeParameter, d = G(e.messageCompiler) ? e.messageCompiler : Fn, b = G(e.messageResolver) ? e.messageResolver : Mn || lr, L = G(e.localeFallbacker) ? e.localeFallbacker : Un || br, h = $(e.fallbackContext) ? e.fallbackContext : void 0, A = e, C = $(A.__datetimeFormatters) ? A.__datetimeFormatters : /* @__PURE__ */ new Map(), B = $(A.__numberFormatters) ? A.__numberFormatters : /* @__PURE__ */ new Map(), te = $(A.__meta) ? A.__meta : {};
  Bt++;
  const W = {
    version: n,
    cid: Bt,
    locale: r,
    fallbackLocale: a,
    messages: s,
    modifiers: _,
    pluralRules: I,
    missing: T,
    missingWarn: g,
    fallbackWarn: v,
    fallbackFormat: S,
    unresolving: P,
    postTranslation: k,
    processor: y,
    warnHtmlMessage: U,
    escapeParameter: m,
    messageCompiler: d,
    messageResolver: b,
    localeFallbacker: L,
    fallbackContext: h,
    onWarn: t,
    __meta: te
  };
  return W.datetimeFormats = i, W.numberFormats = u, W.__datetimeFormatters = C, W.__numberFormatters = B, __INTLIFY_PROD_DEVTOOLS__ && gr(W, n, te), W;
}
const mt = (e) => ({ [e]: H() });
function yt(e, t, n, r, l) {
  const { missing: a, onWarn: s } = e;
  if (a !== null) {
    const i = a(e, n, t, l);
    return p(i) ? i : t;
  } else
    return t;
}
function Je(e, t, n) {
  const r = e;
  r.__localeChainCache = /* @__PURE__ */ new Map(), e.localeFallbacker(e, n, t);
}
function Rr(e, t) {
  return e === t ? !1 : e.split("-")[0] === t.split("-")[0];
}
function Dr(e, t) {
  const n = t.indexOf(e);
  if (n === -1)
    return !1;
  for (let r = n + 1; r < t.length; r++)
    if (Rr(e, t[r]))
      return !0;
  return !1;
}
function dt(e) {
  return (n) => Fr(n, e);
}
function Fr(e, t) {
  const n = ja(t);
  if (n == null)
    throw qe(
      0
      /* NodeTypes.Resource */
    );
  if (At(n) === 1) {
    const a = Ba(n);
    return e.plural(a.reduce((s, i) => [
      ...s,
      Jt(e, i)
    ], []));
  } else
    return Jt(e, n);
}
function Jt(e, t) {
  const n = Ja(t);
  if (n != null)
    return e.type === "text" ? n : e.normalize([n]);
  {
    const r = Qa(t).reduce((l, a) => [...l, gt(e, a)], []);
    return e.normalize(r);
  }
}
function gt(e, t) {
  const n = At(t);
  switch (n) {
    case 3:
      return at(t, n);
    case 9:
      return at(t, n);
    case 4: {
      const r = t;
      if (le(r, "k") && r.k)
        return e.interpolate(e.named(r.k));
      if (le(r, "key") && r.key)
        return e.interpolate(e.named(r.key));
      throw qe(n);
    }
    case 5: {
      const r = t;
      if (le(r, "i") && j(r.i))
        return e.interpolate(e.list(r.i));
      if (le(r, "index") && j(r.index))
        return e.interpolate(e.list(r.index));
      throw qe(n);
    }
    case 6: {
      const r = t, l = qa(r), a = Za(r);
      return e.linked(gt(e, a), l ? gt(e, l) : void 0, e.type);
    }
    case 7:
      return at(t, n);
    case 8:
      return at(t, n);
    default:
      throw new Error(`unhandled node on format message part: ${n}`);
  }
}
const $n = (e) => e;
let ye = H();
function Vn(e, t = {}) {
  let n = !1;
  const r = t.onError || Oa;
  return t.onError = (l) => {
    n = !0, r(l);
  }, { ...Xa(e, t), detectError: n };
}
const Mr = /* @__NO_SIDE_EFFECTS__ */ (e, t) => {
  if (!p(e))
    throw ie(se.NOT_SUPPORT_NON_STRING_MESSAGE);
  {
    w(t.warnHtmlMessage) && t.warnHtmlMessage;
    const r = (t.onCacheKey || $n)(e), l = ye[r];
    if (l)
      return l;
    const { code: a, detectError: s } = Vn(e, t), i = new Function(`return ${a}`)();
    return s ? i : ye[r] = i;
  }
};
function Ur(e, t) {
  if (__INTLIFY_JIT_COMPILATION__ && !__INTLIFY_DROP_MESSAGE_COMPILER__ && p(e)) {
    w(t.warnHtmlMessage) && t.warnHtmlMessage;
    const r = (t.onCacheKey || $n)(e), l = ye[r];
    if (l)
      return l;
    const { ast: a, detectError: s } = Vn(e, {
      ...t,
      location: !1,
      jit: !0
    }), i = dt(a);
    return s ? i : ye[r] = i;
  } else {
    const n = e.cacheKey;
    if (n) {
      const r = ye[n];
      return r || (ye[n] = dt(e));
    } else
      return dt(e);
  }
}
const Qt = () => "", ne = (e) => G(e);
function qt(e, ...t) {
  const { fallbackFormat: n, postTranslation: r, unresolving: l, messageCompiler: a, fallbackLocale: s, messages: i } = e, [u, _] = Lt(...t), I = w(_.missingWarn) ? _.missingWarn : e.missingWarn, T = w(_.fallbackWarn) ? _.fallbackWarn : e.fallbackWarn, g = w(_.escapeParameter) ? _.escapeParameter : e.escapeParameter, v = !!_.resolvedMessage, S = p(_.default) || w(_.default) ? w(_.default) ? a ? u : () => u : _.default : n ? a ? u : () => u : "", P = n || S !== "", k = kt(e, _);
  g && wr(_);
  let [y, U, m] = v ? [
    u,
    k,
    i[k] || H()
  ] : Hn(e, u, k, s, T, I), d = y, b = u;
  if (!v && !(p(d) || ue(d) || ne(d)) && P && (d = S, b = d), !v && (!(p(d) || ue(d) || ne(d)) || !p(U)))
    return l ? it : u;
  let L = !1;
  const h = () => {
    L = !0;
  }, A = ne(d) ? d : Yn(e, u, U, d, b, h);
  if (L)
    return d;
  const C = Vr(e, U, m, _), B = dr(C), te = Wr(e, A, B), W = r ? r(te, u) : te;
  if (__INTLIFY_PROD_DEVTOOLS__) {
    const Z = {
      timestamp: Date.now(),
      key: p(u) ? u : ne(d) ? d.key : "",
      locale: U || (ne(d) ? d.locale : ""),
      format: p(d) ? d : ne(d) ? d.source : "",
      message: W
    };
    Z.meta = q({}, e.__meta, /* @__PURE__ */ kr() || {}), Lr(Z);
  }
  return W;
}
function wr(e) {
  x(e.list) ? e.list = e.list.map((t) => p(t) ? Mt(t) : t) : $(e.named) && Object.keys(e.named).forEach((t) => {
    p(e.named[t]) && (e.named[t] = Mt(e.named[t]));
  });
}
function Hn(e, t, n, r, l, a) {
  const { messages: s, onWarn: i, messageResolver: u, localeFallbacker: _ } = e, I = _(e, r, n);
  let T = H(), g, v = null;
  const S = "translate";
  for (let P = 0; P < I.length && (g = I[P], T = s[g] || H(), (v = u(T, t)) === null && (v = T[t]), !(p(v) || ue(v) || ne(v))); P++)
    if (!Dr(g, I)) {
      const k = yt(
        e,
        // eslint-disable-line @typescript-eslint/no-explicit-any
        t,
        g,
        a,
        S
      );
      k !== t && (v = k);
    }
  return [v, g, T];
}
function Yn(e, t, n, r, l, a) {
  const { messageCompiler: s, warnHtmlMessage: i } = e;
  if (ne(r)) {
    const _ = r;
    return _.locale = _.locale || n, _.key = _.key || t, _;
  }
  if (s == null) {
    const _ = () => r;
    return _.locale = n, _.key = t, _;
  }
  const u = s(r, $r(e, n, l, r, i, a));
  return u.locale = n, u.key = t, u.source = r, u;
}
function Wr(e, t, n) {
  return t(n);
}
function Lt(...e) {
  const [t, n, r] = e, l = H();
  if (!p(t) && !j(t) && !ne(t) && !ue(t))
    throw ie(se.INVALID_ARGUMENT);
  const a = j(t) ? String(t) : (ne(t), t);
  return j(n) ? l.plural = n : p(n) ? l.default = n : F(n) && !ct(n) ? l.named = n : x(n) && (l.list = n), j(r) ? l.plural = r : p(r) ? l.default = r : F(r) && q(l, r), [a, l];
}
function $r(e, t, n, r, l, a) {
  return {
    locale: t,
    key: n,
    warnHtmlMessage: l,
    onError: (s) => {
      throw a && a(s), s;
    },
    onCacheKey: (s) => ca(t, n, s)
  };
}
function Vr(e, t, n, r) {
  const { modifiers: l, pluralRules: a, messageResolver: s, fallbackLocale: i, fallbackWarn: u, missingWarn: _, fallbackContext: I } = e, g = {
    locale: t,
    modifiers: l,
    pluralRules: a,
    messages: (v) => {
      let S = s(n, v);
      if (S == null && I) {
        const [, , P] = Hn(I, v, t, i, u, _);
        S = s(P, v);
      }
      if (p(S) || ue(S)) {
        let P = !1;
        const y = Yn(e, v, t, S, v, () => {
          P = !0;
        });
        return P ? Qt : y;
      } else return ne(S) ? S : Qt;
    }
  };
  return e.processor && (g.processor = e.processor), r.list && (g.list = r.list), r.named && (g.named = r.named), j(r.plural) && (g.pluralIndex = r.plural), g;
}
function Zt(e, ...t) {
  const { datetimeFormats: n, unresolving: r, fallbackLocale: l, onWarn: a, localeFallbacker: s } = e, { __datetimeFormatters: i } = e, [u, _, I, T] = Nt(...t), g = w(I.missingWarn) ? I.missingWarn : e.missingWarn;
  w(I.fallbackWarn) ? I.fallbackWarn : e.fallbackWarn;
  const v = !!I.part, S = kt(e, I), P = s(
    e,
    // eslint-disable-line @typescript-eslint/no-explicit-any
    l,
    S
  );
  if (!p(u) || u === "")
    return new Intl.DateTimeFormat(S, T).format(_);
  let k = {}, y, U = null;
  const m = "datetime format";
  for (let L = 0; L < P.length && (y = P[L], k = n[y] || {}, U = k[u], !F(U)); L++)
    yt(e, u, y, g, m);
  if (!F(U) || !p(y))
    return r ? it : u;
  let d = `${y}__${u}`;
  ct(T) || (d = `${d}__${JSON.stringify(T)}`);
  let b = i.get(d);
  return b || (b = new Intl.DateTimeFormat(y, q({}, U, T)), i.set(d, b)), v ? b.formatToParts(_) : b.format(_);
}
const Gn = [
  "localeMatcher",
  "weekday",
  "era",
  "year",
  "month",
  "day",
  "hour",
  "minute",
  "second",
  "timeZoneName",
  "formatMatcher",
  "hour12",
  "timeZone",
  "dateStyle",
  "timeStyle",
  "calendar",
  "dayPeriod",
  "numberingSystem",
  "hourCycle",
  "fractionalSecondDigits"
];
function Nt(...e) {
  const [t, n, r, l] = e, a = H();
  let s = H(), i;
  if (p(t)) {
    const u = t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);
    if (!u)
      throw ie(se.INVALID_ISO_DATE_ARGUMENT);
    const _ = u[3] ? u[3].trim().startsWith("T") ? `${u[1].trim()}${u[3].trim()}` : `${u[1].trim()}T${u[3].trim()}` : u[1].trim();
    i = new Date(_);
    try {
      i.toISOString();
    } catch {
      throw ie(se.INVALID_ISO_DATE_ARGUMENT);
    }
  } else if (ia(t)) {
    if (isNaN(t.getTime()))
      throw ie(se.INVALID_DATE_ARGUMENT);
    i = t;
  } else if (j(t))
    i = t;
  else
    throw ie(se.INVALID_ARGUMENT);
  return p(n) ? a.key = n : F(n) && Object.keys(n).forEach((u) => {
    Gn.includes(u) ? s[u] = n[u] : a[u] = n[u];
  }), p(r) ? a.locale = r : F(r) && (s = r), F(l) && (s = l), [a.key || "", i, a, s];
}
function zt(e, t, n) {
  const r = e;
  for (const l in n) {
    const a = `${t}__${l}`;
    r.__datetimeFormatters.has(a) && r.__datetimeFormatters.delete(a);
  }
}
function en(e, ...t) {
  const { numberFormats: n, unresolving: r, fallbackLocale: l, onWarn: a, localeFallbacker: s } = e, { __numberFormatters: i } = e, [u, _, I, T] = Tt(...t), g = w(I.missingWarn) ? I.missingWarn : e.missingWarn;
  w(I.fallbackWarn) ? I.fallbackWarn : e.fallbackWarn;
  const v = !!I.part, S = kt(e, I), P = s(
    e,
    // eslint-disable-line @typescript-eslint/no-explicit-any
    l,
    S
  );
  if (!p(u) || u === "")
    return new Intl.NumberFormat(S, T).format(_);
  let k = {}, y, U = null;
  const m = "number format";
  for (let L = 0; L < P.length && (y = P[L], k = n[y] || {}, U = k[u], !F(U)); L++)
    yt(e, u, y, g, m);
  if (!F(U) || !p(y))
    return r ? it : u;
  let d = `${y}__${u}`;
  ct(T) || (d = `${d}__${JSON.stringify(T)}`);
  let b = i.get(d);
  return b || (b = new Intl.NumberFormat(y, q({}, U, T)), i.set(d, b)), v ? b.formatToParts(_) : b.format(_);
}
const xn = [
  "localeMatcher",
  "style",
  "currency",
  "currencyDisplay",
  "currencySign",
  "useGrouping",
  "minimumIntegerDigits",
  "minimumFractionDigits",
  "maximumFractionDigits",
  "minimumSignificantDigits",
  "maximumSignificantDigits",
  "compactDisplay",
  "notation",
  "signDisplay",
  "unit",
  "unitDisplay",
  "roundingMode",
  "roundingPriority",
  "roundingIncrement",
  "trailingZeroDisplay"
];
function Tt(...e) {
  const [t, n, r, l] = e, a = H();
  let s = H();
  if (!j(t))
    throw ie(se.INVALID_ARGUMENT);
  const i = t;
  return p(n) ? a.key = n : F(n) && Object.keys(n).forEach((u) => {
    xn.includes(u) ? s[u] = n[u] : a[u] = n[u];
  }), p(r) ? a.locale = r : F(r) && (s = r), F(l) && (s = l), [a.key || "", i, a, s];
}
function tn(e, t, n) {
  const r = e;
  for (const l in n) {
    const a = `${t}__${l}`;
    r.__numberFormatters.has(a) && r.__numberFormatters.delete(a);
  }
}
Ka();
/*!
  * vue-i18n v9.14.4
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */
const Hr = "9.14.4";
function Yr() {
  typeof __VUE_I18N_FULL_INSTALL__ != "boolean" && (me().__VUE_I18N_FULL_INSTALL__ = !0), typeof __VUE_I18N_LEGACY_API__ != "boolean" && (me().__VUE_I18N_LEGACY_API__ = !0), typeof __INTLIFY_JIT_COMPILATION__ != "boolean" && (me().__INTLIFY_JIT_COMPILATION__ = !1), typeof __INTLIFY_DROP_MESSAGE_COMPILER__ != "boolean" && (me().__INTLIFY_DROP_MESSAGE_COMPILER__ = !1), typeof __INTLIFY_PROD_DEVTOOLS__ != "boolean" && (me().__INTLIFY_PROD_DEVTOOLS__ = !1);
}
const Gr = Ir.__EXTEND_POINT__, _e = ot(Gr);
_e(), _e(), _e(), _e(), _e(), _e(), _e(), _e(), _e();
const Xn = se.__EXTEND_POINT__, ee = ot(Xn), J = {
  // composer module errors
  UNEXPECTED_RETURN_TYPE: Xn,
  // 24
  // legacy module errors
  INVALID_ARGUMENT: ee(),
  // 25
  // i18n module errors
  MUST_BE_CALL_SETUP_TOP: ee(),
  // 26
  NOT_INSTALLED: ee(),
  // 27
  NOT_AVAILABLE_IN_LEGACY_MODE: ee(),
  // 28
  // directive module errors
  REQUIRED_VALUE: ee(),
  // 29
  INVALID_VALUE: ee(),
  // 30
  // vue-devtools errors
  CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN: ee(),
  // 31
  NOT_INSTALLED_WITH_PROVIDE: ee(),
  // 32
  // unexpected error
  UNEXPECTED_ERROR: ee(),
  // 33
  // not compatible legacy vue-i18n constructor
  NOT_COMPATIBLE_LEGACY_VUE_I18N: ee(),
  // 34
  // bridge support vue 2.x only
  BRIDGE_SUPPORT_VUE_2_ONLY: ee(),
  // 35
  // need to define `i18n` option in `allowComposition: true` and `useScope: 'local' at `useI18n``
  MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION: ee(),
  // 36
  // Not available Compostion API in Legacy API mode. Please make sure that the legacy API mode is working properly
  NOT_AVAILABLE_COMPOSITION_IN_LEGACY: ee(),
  // 37
  // for enhancement
  __EXTEND_POINT__: ee()
  // 38
};
function Q(e, ...t) {
  return De(e, null, void 0);
}
const It = /* @__PURE__ */ Le("__translateVNode"), bt = /* @__PURE__ */ Le("__datetimeParts"), Ot = /* @__PURE__ */ Le("__numberParts"), Kn = Le("__setPluralRules"), jn = /* @__PURE__ */ Le("__injectWithOption"), ht = /* @__PURE__ */ Le("__dispose");
function ze(e) {
  if (!$(e) || ue(e))
    return e;
  for (const t in e)
    if (le(e, t))
      if (!t.includes("."))
        $(e[t]) && ze(e[t]);
      else {
        const n = t.split("."), r = n.length - 1;
        let l = e, a = !1;
        for (let s = 0; s < r; s++) {
          if (n[s] === "__proto__")
            throw new Error(`unsafe key: ${n[s]}`);
          if (n[s] in l || (l[n[s]] = H()), !$(l[n[s]])) {
            a = !0;
            break;
          }
          l = l[n[s]];
        }
        if (a || (ue(l) ? Sn.includes(n[r]) || delete e[t] : (l[n[r]] = e[t], delete e[t])), !ue(l)) {
          const s = l[n[r]];
          $(s) && ze(s);
        }
      }
  return e;
}
function ut(e, t) {
  const { messages: n, __i18n: r, messageResolver: l, flatJson: a } = t, s = F(n) ? n : x(r) ? H() : { [e]: H() };
  if (x(r) && r.forEach((i) => {
    if ("locale" in i && "resource" in i) {
      const { locale: u, resource: _ } = i;
      u ? (s[u] = s[u] || H(), rt(_, s[u])) : rt(_, s);
    } else
      p(i) && rt(JSON.parse(i), s);
  }), l == null && a)
    for (const i in s)
      le(s, i) && ze(s[i]);
  return s;
}
function Bn(e) {
  return e.type;
}
function Jn(e, t, n) {
  let r = $(t.messages) ? t.messages : H();
  "__i18nGlobal" in n && (r = ut(e.locale.value, {
    messages: r,
    __i18n: n.__i18nGlobal
  }));
  const l = Object.keys(r);
  l.length && l.forEach((a) => {
    e.mergeLocaleMessage(a, r[a]);
  });
  {
    if ($(t.datetimeFormats)) {
      const a = Object.keys(t.datetimeFormats);
      a.length && a.forEach((s) => {
        e.mergeDateTimeFormat(s, t.datetimeFormats[s]);
      });
    }
    if ($(t.numberFormats)) {
      const a = Object.keys(t.numberFormats);
      a.length && a.forEach((s) => {
        e.mergeNumberFormat(s, t.numberFormats[s]);
      });
    }
  }
}
function nn(e) {
  return la(sa, null, e, 0);
}
const an = "__INTLIFY_META__", rn = () => [], xr = () => !1;
let ln = 0;
function sn(e) {
  return (t, n, r, l) => e(n, r, Qe() || void 0, l);
}
const Xr = /* @__NO_SIDE_EFFECTS__ */ () => {
  const e = Qe();
  let t = null;
  return e && (t = Bn(e)[an]) ? { [an]: t } : null;
};
function St(e = {}, t) {
  const { __root: n, __injectWithOption: r } = e, l = n === void 0, a = e.flatJson, s = lt ? Ae : En, i = !!e.translateExistCompatible;
  let u = w(e.inheritLocale) ? e.inheritLocale : !0;
  const _ = s(
    // prettier-ignore
    n && u ? n.locale.value : p(e.locale) ? e.locale : Re
  ), I = s(
    // prettier-ignore
    n && u ? n.fallbackLocale.value : p(e.fallbackLocale) || x(e.fallbackLocale) || F(e.fallbackLocale) || e.fallbackLocale === !1 ? e.fallbackLocale : _.value
  ), T = s(ut(_.value, e)), g = s(F(e.datetimeFormats) ? e.datetimeFormats : { [_.value]: {} }), v = s(F(e.numberFormats) ? e.numberFormats : { [_.value]: {} });
  let S = n ? n.missingWarn : w(e.missingWarn) || ge(e.missingWarn) ? e.missingWarn : !0, P = n ? n.fallbackWarn : w(e.fallbackWarn) || ge(e.fallbackWarn) ? e.fallbackWarn : !0, k = n ? n.fallbackRoot : w(e.fallbackRoot) ? e.fallbackRoot : !0, y = !!e.fallbackFormat, U = G(e.missing) ? e.missing : null, m = G(e.missing) ? sn(e.missing) : null, d = G(e.postTranslation) ? e.postTranslation : null, b = n ? n.warnHtmlMessage : w(e.warnHtmlMessage) ? e.warnHtmlMessage : !0, L = !!e.escapeParameter;
  const h = n ? n.modifiers : F(e.modifiers) ? e.modifiers : {};
  let A = e.pluralRules || n && n.pluralRules, C;
  C = (() => {
    l && jt(null);
    const f = {
      version: Hr,
      locale: _.value,
      fallbackLocale: I.value,
      messages: T.value,
      modifiers: h,
      pluralRules: A,
      missing: m === null ? void 0 : m,
      missingWarn: S,
      fallbackWarn: P,
      fallbackFormat: y,
      unresolving: !0,
      postTranslation: d === null ? void 0 : d,
      warnHtmlMessage: b,
      escapeParameter: L,
      messageResolver: e.messageResolver,
      messageCompiler: e.messageCompiler,
      __meta: { framework: "vue" }
    };
    f.datetimeFormats = g.value, f.numberFormats = v.value, f.__datetimeFormatters = F(C) ? C.__datetimeFormatters : void 0, f.__numberFormatters = F(C) ? C.__numberFormatters : void 0;
    const N = Sr(f);
    return l && jt(N), N;
  })(), Je(C, _.value, I.value);
  function te() {
    return [
      _.value,
      I.value,
      T.value,
      g.value,
      v.value
    ];
  }
  const W = oe({
    get: () => _.value,
    set: (f) => {
      _.value = f, C.locale = _.value;
    }
  }), Z = oe({
    get: () => I.value,
    set: (f) => {
      I.value = f, C.fallbackLocale = I.value, Je(C, _.value, f);
    }
  }), Fe = oe(() => T.value), Me = /* @__PURE__ */ oe(() => g.value), ce = /* @__PURE__ */ oe(() => v.value);
  function Ue() {
    return G(d) ? d : null;
  }
  function we(f) {
    d = f, C.postTranslation = f;
  }
  function We() {
    return U;
  }
  function $e(f) {
    f !== null && (m = sn(f)), U = f, C.missing = m;
  }
  const ae = (f, N, Y, K, de, tt) => {
    te();
    let Pe;
    try {
      __INTLIFY_PROD_DEVTOOLS__, l || (C.fallbackContext = n ? yr() : void 0), Pe = f(C);
    } finally {
      __INTLIFY_PROD_DEVTOOLS__, l || (C.fallbackContext = void 0);
    }
    if (Y !== "translate exists" && // for not `te` (e.g `t`)
    j(Pe) && Pe === it || Y === "translate exists" && !Pe) {
      const [Zn, ml] = N();
      return n && k ? K(n) : de(Zn);
    } else {
      if (tt(Pe))
        return Pe;
      throw Q(J.UNEXPECTED_RETURN_TYPE);
    }
  };
  function Ie(...f) {
    return ae((N) => Reflect.apply(qt, null, [N, ...f]), () => Lt(...f), "translate", (N) => Reflect.apply(N.t, N, [...f]), (N) => N, (N) => p(N));
  }
  function Ve(...f) {
    const [N, Y, K] = f;
    if (K && !$(K))
      throw Q(J.INVALID_ARGUMENT);
    return Ie(N, Y, q({ resolvedMessage: !0 }, K || {}));
  }
  function he(...f) {
    return ae((N) => Reflect.apply(Zt, null, [N, ...f]), () => Nt(...f), "datetime format", (N) => Reflect.apply(N.d, N, [...f]), () => xt, (N) => p(N));
  }
  function He(...f) {
    return ae((N) => Reflect.apply(en, null, [N, ...f]), () => Tt(...f), "number format", (N) => Reflect.apply(N.n, N, [...f]), () => xt, (N) => p(N));
  }
  function Ye(f) {
    return f.map((N) => p(N) || j(N) || w(N) ? nn(String(N)) : N);
  }
  const Ge = {
    normalize: Ye,
    interpolate: (f) => f,
    type: "vnode"
  };
  function xe(...f) {
    return ae(
      (N) => {
        let Y;
        const K = N;
        try {
          K.processor = Ge, Y = Reflect.apply(qt, null, [K, ...f]);
        } finally {
          K.processor = null;
        }
        return Y;
      },
      () => Lt(...f),
      "translate",
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (N) => N[It](...f),
      (N) => [nn(N)],
      (N) => x(N)
    );
  }
  function pe(...f) {
    return ae(
      (N) => Reflect.apply(en, null, [N, ...f]),
      () => Tt(...f),
      "number format",
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (N) => N[Ot](...f),
      rn,
      (N) => p(N) || x(N)
    );
  }
  function Xe(...f) {
    return ae(
      (N) => Reflect.apply(Zt, null, [N, ...f]),
      () => Nt(...f),
      "datetime format",
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (N) => N[bt](...f),
      rn,
      (N) => p(N) || x(N)
    );
  }
  function Ke(f) {
    A = f, C.pluralRules = A;
  }
  function je(f, N) {
    return ae(() => {
      if (!f)
        return !1;
      const Y = p(N) ? N : _.value, K = ve(Y), de = C.messageResolver(K, f);
      return i ? de != null : ue(de) || ne(de) || p(de);
    }, () => [f], "translate exists", (Y) => Reflect.apply(Y.te, Y, [f, N]), xr, (Y) => w(Y));
  }
  function O(f) {
    let N = null;
    const Y = Dn(C, I.value, _.value);
    for (let K = 0; K < Y.length; K++) {
      const de = T.value[Y[K]] || {}, tt = C.messageResolver(de, f);
      if (tt != null) {
        N = tt;
        break;
      }
    }
    return N;
  }
  function V(f) {
    const N = O(f);
    return N ?? (n ? n.tm(f) || {} : {});
  }
  function ve(f) {
    return T.value[f] || {};
  }
  function Ce(f, N) {
    if (a) {
      const Y = { [f]: N };
      for (const K in Y)
        le(Y, K) && ze(Y[K]);
      N = Y[f];
    }
    T.value[f] = N, C.messages = T.value;
  }
  function Be(f, N) {
    T.value[f] = T.value[f] || {};
    const Y = { [f]: N };
    if (a)
      for (const K in Y)
        le(Y, K) && ze(Y[K]);
    N = Y[f], rt(N, T.value[f]), C.messages = T.value;
  }
  function ft(f) {
    return g.value[f] || {};
  }
  function c(f, N) {
    g.value[f] = N, C.datetimeFormats = g.value, zt(C, f, N);
  }
  function o(f, N) {
    g.value[f] = q(g.value[f] || {}, N), C.datetimeFormats = g.value, zt(C, f, N);
  }
  function E(f) {
    return v.value[f] || {};
  }
  function R(f, N) {
    v.value[f] = N, C.numberFormats = v.value, tn(C, f, N);
  }
  function X(f, N) {
    v.value[f] = q(v.value[f] || {}, N), C.numberFormats = v.value, tn(C, f, N);
  }
  ln++, n && lt && (Et(n.locale, (f) => {
    u && (_.value = f, C.locale = f, Je(C, _.value, I.value));
  }), Et(n.fallbackLocale, (f) => {
    u && (I.value = f, C.fallbackLocale = f, Je(C, _.value, I.value));
  }));
  const M = {
    id: ln,
    locale: W,
    fallbackLocale: Z,
    get inheritLocale() {
      return u;
    },
    set inheritLocale(f) {
      u = f, f && n && (_.value = n.locale.value, I.value = n.fallbackLocale.value, Je(C, _.value, I.value));
    },
    get availableLocales() {
      return Object.keys(T.value).sort();
    },
    messages: Fe,
    get modifiers() {
      return h;
    },
    get pluralRules() {
      return A || {};
    },
    get isGlobal() {
      return l;
    },
    get missingWarn() {
      return S;
    },
    set missingWarn(f) {
      S = f, C.missingWarn = S;
    },
    get fallbackWarn() {
      return P;
    },
    set fallbackWarn(f) {
      P = f, C.fallbackWarn = P;
    },
    get fallbackRoot() {
      return k;
    },
    set fallbackRoot(f) {
      k = f;
    },
    get fallbackFormat() {
      return y;
    },
    set fallbackFormat(f) {
      y = f, C.fallbackFormat = y;
    },
    get warnHtmlMessage() {
      return b;
    },
    set warnHtmlMessage(f) {
      b = f, C.warnHtmlMessage = f;
    },
    get escapeParameter() {
      return L;
    },
    set escapeParameter(f) {
      L = f, C.escapeParameter = f;
    },
    t: Ie,
    getLocaleMessage: ve,
    setLocaleMessage: Ce,
    mergeLocaleMessage: Be,
    getPostTranslationHandler: Ue,
    setPostTranslationHandler: we,
    getMissingHandler: We,
    setMissingHandler: $e,
    [Kn]: Ke
  };
  return M.datetimeFormats = Me, M.numberFormats = ce, M.rt = Ve, M.te = je, M.tm = V, M.d = he, M.n = He, M.getDateTimeFormat = ft, M.setDateTimeFormat = c, M.mergeDateTimeFormat = o, M.getNumberFormat = E, M.setNumberFormat = R, M.mergeNumberFormat = X, M[jn] = r, M[It] = xe, M[bt] = Xe, M[Ot] = pe, M;
}
function Kr(e) {
  const t = p(e.locale) ? e.locale : Re, n = p(e.fallbackLocale) || x(e.fallbackLocale) || F(e.fallbackLocale) || e.fallbackLocale === !1 ? e.fallbackLocale : t, r = G(e.missing) ? e.missing : void 0, l = w(e.silentTranslationWarn) || ge(e.silentTranslationWarn) ? !e.silentTranslationWarn : !0, a = w(e.silentFallbackWarn) || ge(e.silentFallbackWarn) ? !e.silentFallbackWarn : !0, s = w(e.fallbackRoot) ? e.fallbackRoot : !0, i = !!e.formatFallbackMessages, u = F(e.modifiers) ? e.modifiers : {}, _ = e.pluralizationRules, I = G(e.postTranslation) ? e.postTranslation : void 0, T = p(e.warnHtmlInMessage) ? e.warnHtmlInMessage !== "off" : !0, g = !!e.escapeParameterHtml, v = w(e.sync) ? e.sync : !0;
  let S = e.messages;
  if (F(e.sharedMessages)) {
    const L = e.sharedMessages;
    S = Object.keys(L).reduce((A, C) => {
      const B = A[C] || (A[C] = {});
      return q(B, L[C]), A;
    }, S || {});
  }
  const { __i18n: P, __root: k, __injectWithOption: y } = e, U = e.datetimeFormats, m = e.numberFormats, d = e.flatJson, b = e.translateExistCompatible;
  return {
    locale: t,
    fallbackLocale: n,
    messages: S,
    flatJson: d,
    datetimeFormats: U,
    numberFormats: m,
    missing: r,
    missingWarn: l,
    fallbackWarn: a,
    fallbackRoot: s,
    fallbackFormat: i,
    modifiers: u,
    pluralRules: _,
    postTranslation: I,
    warnHtmlMessage: T,
    escapeParameter: g,
    messageResolver: e.messageResolver,
    inheritLocale: v,
    translateExistCompatible: b,
    __i18n: P,
    __root: k,
    __injectWithOption: y
  };
}
function pt(e = {}, t) {
  {
    const n = St(Kr(e)), { __extender: r } = e, l = {
      // id
      id: n.id,
      // locale
      get locale() {
        return n.locale.value;
      },
      set locale(a) {
        n.locale.value = a;
      },
      // fallbackLocale
      get fallbackLocale() {
        return n.fallbackLocale.value;
      },
      set fallbackLocale(a) {
        n.fallbackLocale.value = a;
      },
      // messages
      get messages() {
        return n.messages.value;
      },
      // datetimeFormats
      get datetimeFormats() {
        return n.datetimeFormats.value;
      },
      // numberFormats
      get numberFormats() {
        return n.numberFormats.value;
      },
      // availableLocales
      get availableLocales() {
        return n.availableLocales;
      },
      // formatter
      get formatter() {
        return {
          interpolate() {
            return [];
          }
        };
      },
      set formatter(a) {
      },
      // missing
      get missing() {
        return n.getMissingHandler();
      },
      set missing(a) {
        n.setMissingHandler(a);
      },
      // silentTranslationWarn
      get silentTranslationWarn() {
        return w(n.missingWarn) ? !n.missingWarn : n.missingWarn;
      },
      set silentTranslationWarn(a) {
        n.missingWarn = w(a) ? !a : a;
      },
      // silentFallbackWarn
      get silentFallbackWarn() {
        return w(n.fallbackWarn) ? !n.fallbackWarn : n.fallbackWarn;
      },
      set silentFallbackWarn(a) {
        n.fallbackWarn = w(a) ? !a : a;
      },
      // modifiers
      get modifiers() {
        return n.modifiers;
      },
      // formatFallbackMessages
      get formatFallbackMessages() {
        return n.fallbackFormat;
      },
      set formatFallbackMessages(a) {
        n.fallbackFormat = a;
      },
      // postTranslation
      get postTranslation() {
        return n.getPostTranslationHandler();
      },
      set postTranslation(a) {
        n.setPostTranslationHandler(a);
      },
      // sync
      get sync() {
        return n.inheritLocale;
      },
      set sync(a) {
        n.inheritLocale = a;
      },
      // warnInHtmlMessage
      get warnHtmlInMessage() {
        return n.warnHtmlMessage ? "warn" : "off";
      },
      set warnHtmlInMessage(a) {
        n.warnHtmlMessage = a !== "off";
      },
      // escapeParameterHtml
      get escapeParameterHtml() {
        return n.escapeParameter;
      },
      set escapeParameterHtml(a) {
        n.escapeParameter = a;
      },
      // preserveDirectiveContent
      get preserveDirectiveContent() {
        return !0;
      },
      set preserveDirectiveContent(a) {
      },
      // pluralizationRules
      get pluralizationRules() {
        return n.pluralRules || {};
      },
      // for internal
      __composer: n,
      // t
      t(...a) {
        const [s, i, u] = a, _ = {};
        let I = null, T = null;
        if (!p(s))
          throw Q(J.INVALID_ARGUMENT);
        const g = s;
        return p(i) ? _.locale = i : x(i) ? I = i : F(i) && (T = i), x(u) ? I = u : F(u) && (T = u), Reflect.apply(n.t, n, [
          g,
          I || T || {},
          _
        ]);
      },
      rt(...a) {
        return Reflect.apply(n.rt, n, [...a]);
      },
      // tc
      tc(...a) {
        const [s, i, u] = a, _ = { plural: 1 };
        let I = null, T = null;
        if (!p(s))
          throw Q(J.INVALID_ARGUMENT);
        const g = s;
        return p(i) ? _.locale = i : j(i) ? _.plural = i : x(i) ? I = i : F(i) && (T = i), p(u) ? _.locale = u : x(u) ? I = u : F(u) && (T = u), Reflect.apply(n.t, n, [
          g,
          I || T || {},
          _
        ]);
      },
      // te
      te(a, s) {
        return n.te(a, s);
      },
      // tm
      tm(a) {
        return n.tm(a);
      },
      // getLocaleMessage
      getLocaleMessage(a) {
        return n.getLocaleMessage(a);
      },
      // setLocaleMessage
      setLocaleMessage(a, s) {
        n.setLocaleMessage(a, s);
      },
      // mergeLocaleMessage
      mergeLocaleMessage(a, s) {
        n.mergeLocaleMessage(a, s);
      },
      // d
      d(...a) {
        return Reflect.apply(n.d, n, [...a]);
      },
      // getDateTimeFormat
      getDateTimeFormat(a) {
        return n.getDateTimeFormat(a);
      },
      // setDateTimeFormat
      setDateTimeFormat(a, s) {
        n.setDateTimeFormat(a, s);
      },
      // mergeDateTimeFormat
      mergeDateTimeFormat(a, s) {
        n.mergeDateTimeFormat(a, s);
      },
      // n
      n(...a) {
        return Reflect.apply(n.n, n, [...a]);
      },
      // getNumberFormat
      getNumberFormat(a) {
        return n.getNumberFormat(a);
      },
      // setNumberFormat
      setNumberFormat(a, s) {
        n.setNumberFormat(a, s);
      },
      // mergeNumberFormat
      mergeNumberFormat(a, s) {
        n.mergeNumberFormat(a, s);
      },
      // getChoiceIndex
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      getChoiceIndex(a, s) {
        return -1;
      }
    };
    return l.__extender = r, l;
  }
}
const Rt = {
  tag: {
    type: [String, Object]
  },
  locale: {
    type: String
  },
  scope: {
    type: String,
    // NOTE: avoid https://github.com/microsoft/rushstack/issues/1050
    validator: (e) => e === "parent" || e === "global",
    default: "parent"
    /* ComponentI18nScope */
  },
  i18n: {
    type: Object
  }
};
function jr({ slots: e }, t) {
  return t.length === 1 && t[0] === "default" ? (e.default ? e.default() : []).reduce((r, l) => [
    ...r,
    // prettier-ignore
    ...l.type === Ln ? l.children : [l]
  ], []) : t.reduce((n, r) => {
    const l = e[r];
    return l && (n[r] = l()), n;
  }, H());
}
function Qn(e) {
  return Ln;
}
const Br = /* @__PURE__ */ vt({
  /* eslint-disable */
  name: "i18n-t",
  props: q({
    keypath: {
      type: String,
      required: !0
    },
    plural: {
      type: [Number, String],
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      validator: (e) => j(e) || !isNaN(e)
    }
  }, Rt),
  /* eslint-enable */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  setup(e, t) {
    const { slots: n, attrs: r } = t, l = e.i18n || Dt({
      useScope: e.scope,
      __useComponent: !0
    });
    return () => {
      const a = Object.keys(n).filter((T) => T !== "_"), s = H();
      e.locale && (s.locale = e.locale), e.plural !== void 0 && (s.plural = p(e.plural) ? +e.plural : e.plural);
      const i = jr(t, a), u = l[It](e.keypath, i, s), _ = q(H(), r), I = p(e.tag) || $(e.tag) ? e.tag : Qn();
      return gn(I, _, u);
    };
  }
}), cn = Br;
function Jr(e) {
  return x(e) && !p(e[0]);
}
function qn(e, t, n, r) {
  const { slots: l, attrs: a } = t;
  return () => {
    const s = { part: !0 };
    let i = H();
    e.locale && (s.locale = e.locale), p(e.format) ? s.key = e.format : $(e.format) && (p(e.format.key) && (s.key = e.format.key), i = Object.keys(e.format).reduce((g, v) => n.includes(v) ? q(H(), g, { [v]: e.format[v] }) : g, H()));
    const u = r(e.value, s, i);
    let _ = [s.key];
    x(u) ? _ = u.map((g, v) => {
      const S = l[g.type], P = S ? S({ [g.type]: g.value, index: v, parts: u }) : [g.value];
      return Jr(P) && (P[0].key = `${g.type}-${v}`), P;
    }) : p(u) && (_ = [u]);
    const I = q(H(), a), T = p(e.tag) || $(e.tag) ? e.tag : Qn();
    return gn(T, I, _);
  };
}
const Qr = /* @__PURE__ */ vt({
  /* eslint-disable */
  name: "i18n-n",
  props: q({
    value: {
      type: Number,
      required: !0
    },
    format: {
      type: [String, Object]
    }
  }, Rt),
  /* eslint-enable */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  setup(e, t) {
    const n = e.i18n || Dt({
      useScope: e.scope,
      __useComponent: !0
    });
    return qn(e, t, xn, (...r) => (
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      n[Ot](...r)
    ));
  }
}), on = Qr, qr = /* @__PURE__ */ vt({
  /* eslint-disable */
  name: "i18n-d",
  props: q({
    value: {
      type: [Number, Date],
      required: !0
    },
    format: {
      type: [String, Object]
    }
  }, Rt),
  /* eslint-enable */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  setup(e, t) {
    const n = e.i18n || Dt({
      useScope: e.scope,
      __useComponent: !0
    });
    return qn(e, t, Gn, (...r) => (
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      n[bt](...r)
    ));
  }
}), un = qr;
function Zr(e, t) {
  const n = e;
  if (e.mode === "composition")
    return n.__getInstance(t) || e.global;
  {
    const r = n.__getInstance(t);
    return r != null ? r.__composer : e.global.__composer;
  }
}
function zr(e) {
  const t = (s) => {
    const { instance: i, modifiers: u, value: _ } = s;
    if (!i || !i.$)
      throw Q(J.UNEXPECTED_ERROR);
    const I = Zr(e, i.$), T = fn(_);
    return [
      Reflect.apply(I.t, I, [..._n(T)]),
      I
    ];
  };
  return {
    created: (s, i) => {
      const [u, _] = t(i);
      lt && e.global === _ && (s.__i18nWatcher = Et(_.locale, () => {
        i.instance && i.instance.$forceUpdate();
      })), s.__composer = _, s.textContent = u;
    },
    unmounted: (s) => {
      lt && s.__i18nWatcher && (s.__i18nWatcher(), s.__i18nWatcher = void 0, delete s.__i18nWatcher), s.__composer && (s.__composer = void 0, delete s.__composer);
    },
    beforeUpdate: (s, { value: i }) => {
      if (s.__composer) {
        const u = s.__composer, _ = fn(i);
        s.textContent = Reflect.apply(u.t, u, [
          ..._n(_)
        ]);
      }
    },
    getSSRProps: (s) => {
      const [i] = t(s);
      return { textContent: i };
    }
  };
}
function fn(e) {
  if (p(e))
    return { path: e };
  if (F(e)) {
    if (!("path" in e))
      throw Q(J.REQUIRED_VALUE, "path");
    return e;
  } else
    throw Q(J.INVALID_VALUE);
}
function _n(e) {
  const { path: t, locale: n, args: r, choice: l, plural: a } = e, s = {}, i = r || {};
  return p(n) && (s.locale = n), j(l) && (s.plural = l), j(a) && (s.plural = a), [t, i, s];
}
function el(e, t, ...n) {
  const r = F(n[0]) ? n[0] : {}, l = !!r.useI18nComponentName;
  (w(r.globalInstall) ? r.globalInstall : !0) && ([l ? "i18n" : cn.name, "I18nT"].forEach((s) => e.component(s, cn)), [on.name, "I18nN"].forEach((s) => e.component(s, on)), [un.name, "I18nD"].forEach((s) => e.component(s, un))), e.directive("t", zr(t));
}
function tl(e, t, n) {
  return {
    beforeCreate() {
      const r = Qe();
      if (!r)
        throw Q(J.UNEXPECTED_ERROR);
      const l = this.$options;
      if (l.i18n) {
        const a = l.i18n;
        if (l.__i18n && (a.__i18n = l.__i18n), a.__root = t, this === this.$root)
          this.$i18n = mn(e, a);
        else {
          a.__injectWithOption = !0, a.__extender = n.__vueI18nExtend, this.$i18n = pt(a);
          const s = this.$i18n;
          s.__extender && (s.__disposer = s.__extender(this.$i18n));
        }
      } else if (l.__i18n)
        if (this === this.$root)
          this.$i18n = mn(e, l);
        else {
          this.$i18n = pt({
            __i18n: l.__i18n,
            __injectWithOption: !0,
            __extender: n.__vueI18nExtend,
            __root: t
          });
          const a = this.$i18n;
          a.__extender && (a.__disposer = a.__extender(this.$i18n));
        }
      else
        this.$i18n = e;
      l.__i18nGlobal && Jn(t, l, l), this.$t = (...a) => this.$i18n.t(...a), this.$rt = (...a) => this.$i18n.rt(...a), this.$tc = (...a) => this.$i18n.tc(...a), this.$te = (a, s) => this.$i18n.te(a, s), this.$d = (...a) => this.$i18n.d(...a), this.$n = (...a) => this.$i18n.n(...a), this.$tm = (a) => this.$i18n.tm(a), n.__setInstance(r, this.$i18n);
    },
    mounted() {
    },
    unmounted() {
      const r = Qe();
      if (!r)
        throw Q(J.UNEXPECTED_ERROR);
      const l = this.$i18n;
      delete this.$t, delete this.$rt, delete this.$tc, delete this.$te, delete this.$d, delete this.$n, delete this.$tm, l.__disposer && (l.__disposer(), delete l.__disposer, delete l.__extender), n.__deleteInstance(r), delete this.$i18n;
    }
  };
}
function mn(e, t) {
  e.locale = t.locale || e.locale, e.fallbackLocale = t.fallbackLocale || e.fallbackLocale, e.missing = t.missing || e.missing, e.silentTranslationWarn = t.silentTranslationWarn || e.silentFallbackWarn, e.silentFallbackWarn = t.silentFallbackWarn || e.silentFallbackWarn, e.formatFallbackMessages = t.formatFallbackMessages || e.formatFallbackMessages, e.postTranslation = t.postTranslation || e.postTranslation, e.warnHtmlInMessage = t.warnHtmlInMessage || e.warnHtmlInMessage, e.escapeParameterHtml = t.escapeParameterHtml || e.escapeParameterHtml, e.sync = t.sync || e.sync, e.__composer[Kn](t.pluralizationRules || e.pluralizationRules);
  const n = ut(e.locale, {
    messages: t.messages,
    __i18n: t.__i18n
  });
  return Object.keys(n).forEach((r) => e.mergeLocaleMessage(r, n[r])), t.datetimeFormats && Object.keys(t.datetimeFormats).forEach((r) => e.mergeDateTimeFormat(r, t.datetimeFormats[r])), t.numberFormats && Object.keys(t.numberFormats).forEach((r) => e.mergeNumberFormat(r, t.numberFormats[r])), e;
}
const nl = /* @__PURE__ */ Le("global-vue-i18n");
function El(e = {}, t) {
  const n = __VUE_I18N_LEGACY_API__ && w(e.legacy) ? e.legacy : __VUE_I18N_LEGACY_API__, r = w(e.globalInjection) ? e.globalInjection : !0, l = __VUE_I18N_LEGACY_API__ && n ? !!e.allowComposition : !0, a = /* @__PURE__ */ new Map(), [s, i] = al(e, n), u = /* @__PURE__ */ Le("");
  function _(g) {
    return a.get(g) || null;
  }
  function I(g, v) {
    a.set(g, v);
  }
  function T(g) {
    a.delete(g);
  }
  {
    const g = {
      // mode
      get mode() {
        return __VUE_I18N_LEGACY_API__ && n ? "legacy" : "composition";
      },
      // allowComposition
      get allowComposition() {
        return l;
      },
      // install plugin
      async install(v, ...S) {
        if (v.__VUE_I18N_SYMBOL__ = u, v.provide(v.__VUE_I18N_SYMBOL__, g), F(S[0])) {
          const y = S[0];
          g.__composerExtend = y.__composerExtend, g.__vueI18nExtend = y.__vueI18nExtend;
        }
        let P = null;
        !n && r && (P = _l(v, g.global)), __VUE_I18N_FULL_INSTALL__ && el(v, g, ...S), __VUE_I18N_LEGACY_API__ && n && v.mixin(tl(i, i.__composer, g));
        const k = v.unmount;
        v.unmount = () => {
          P && P(), g.dispose(), k();
        };
      },
      // global accessor
      get global() {
        return i;
      },
      dispose() {
        s.stop();
      },
      // @internal
      __instances: a,
      // @internal
      __getInstance: _,
      // @internal
      __setInstance: I,
      // @internal
      __deleteInstance: T
    };
    return g;
  }
}
function Dt(e = {}) {
  const t = Qe();
  if (t == null)
    throw Q(J.MUST_BE_CALL_SETUP_TOP);
  if (!t.isCE && t.appContext.app != null && !t.appContext.app.__VUE_I18N_SYMBOL__)
    throw Q(J.NOT_INSTALLED);
  const n = rl(t), r = sl(n), l = Bn(t), a = ll(e, l);
  if (__VUE_I18N_LEGACY_API__ && n.mode === "legacy" && !e.__useComponent) {
    if (!n.allowComposition)
      throw Q(J.NOT_AVAILABLE_IN_LEGACY_MODE);
    return ul(t, a, r, e);
  }
  if (a === "global")
    return Jn(r, e, l), r;
  if (a === "parent") {
    let u = cl(n, t, e.__useComponent);
    return u == null && (u = r), u;
  }
  const s = n;
  let i = s.__getInstance(t);
  if (i == null) {
    const u = q({}, e);
    "__i18n" in l && (u.__i18n = l.__i18n), r && (u.__root = r), i = St(u), s.__composerExtend && (i[ht] = s.__composerExtend(i)), il(s, t, i), s.__setInstance(t, i);
  }
  return i;
}
function al(e, t, n) {
  const r = zn();
  {
    const l = __VUE_I18N_LEGACY_API__ && t ? r.run(() => pt(e)) : r.run(() => St(e));
    if (l == null)
      throw Q(J.UNEXPECTED_ERROR);
    return [r, l];
  }
}
function rl(e) {
  {
    const t = ta(e.isCE ? nl : e.appContext.app.__VUE_I18N_SYMBOL__);
    if (!t)
      throw Q(e.isCE ? J.NOT_INSTALLED_WITH_PROVIDE : J.UNEXPECTED_ERROR);
    return t;
  }
}
function ll(e, t) {
  return ct(e) ? "__i18n" in t ? "local" : "global" : e.useScope ? e.useScope : "local";
}
function sl(e) {
  return e.mode === "composition" ? e.global : e.global.__composer;
}
function cl(e, t, n = !1) {
  let r = null;
  const l = t.root;
  let a = ol(t, n);
  for (; a != null; ) {
    const s = e;
    if (e.mode === "composition")
      r = s.__getInstance(a);
    else if (__VUE_I18N_LEGACY_API__) {
      const i = s.__getInstance(a);
      i != null && (r = i.__composer, n && r && !r[jn] && (r = null));
    }
    if (r != null || l === a)
      break;
    a = a.parent;
  }
  return r;
}
function ol(e, t = !1) {
  return e == null ? null : t && e.vnode.ctx || e.parent;
}
function il(e, t, n) {
  aa(() => {
  }, t), ra(() => {
    const r = n;
    e.__deleteInstance(t);
    const l = r[ht];
    l && (l(), delete r[ht]);
  }, t);
}
function ul(e, t, n, r = {}) {
  const l = t === "local", a = En(null);
  if (l && e.proxy && !(e.proxy.$options.i18n || e.proxy.$options.__i18n))
    throw Q(J.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION);
  const s = w(r.inheritLocale) ? r.inheritLocale : !p(r.locale), i = Ae(
    // prettier-ignore
    !l || s ? n.locale.value : p(r.locale) ? r.locale : Re
  ), u = Ae(
    // prettier-ignore
    !l || s ? n.fallbackLocale.value : p(r.fallbackLocale) || x(r.fallbackLocale) || F(r.fallbackLocale) || r.fallbackLocale === !1 ? r.fallbackLocale : i.value
  ), _ = Ae(ut(i.value, r)), I = Ae(F(r.datetimeFormats) ? r.datetimeFormats : { [i.value]: {} }), T = Ae(F(r.numberFormats) ? r.numberFormats : { [i.value]: {} }), g = l ? n.missingWarn : w(r.missingWarn) || ge(r.missingWarn) ? r.missingWarn : !0, v = l ? n.fallbackWarn : w(r.fallbackWarn) || ge(r.fallbackWarn) ? r.fallbackWarn : !0, S = l ? n.fallbackRoot : w(r.fallbackRoot) ? r.fallbackRoot : !0, P = !!r.fallbackFormat, k = G(r.missing) ? r.missing : null, y = G(r.postTranslation) ? r.postTranslation : null, U = l ? n.warnHtmlMessage : w(r.warnHtmlMessage) ? r.warnHtmlMessage : !0, m = !!r.escapeParameter, d = l ? n.modifiers : F(r.modifiers) ? r.modifiers : {}, b = r.pluralRules || l && n.pluralRules;
  function L() {
    return [
      i.value,
      u.value,
      _.value,
      I.value,
      T.value
    ];
  }
  const h = oe({
    get: () => a.value ? a.value.locale.value : i.value,
    set: (O) => {
      a.value && (a.value.locale.value = O), i.value = O;
    }
  }), A = oe({
    get: () => a.value ? a.value.fallbackLocale.value : u.value,
    set: (O) => {
      a.value && (a.value.fallbackLocale.value = O), u.value = O;
    }
  }), C = oe(() => a.value ? a.value.messages.value : _.value), B = oe(() => I.value), te = oe(() => T.value);
  function W() {
    return a.value ? a.value.getPostTranslationHandler() : y;
  }
  function Z(O) {
    a.value && a.value.setPostTranslationHandler(O);
  }
  function Fe() {
    return a.value ? a.value.getMissingHandler() : k;
  }
  function Me(O) {
    a.value && a.value.setMissingHandler(O);
  }
  function ce(O) {
    return L(), O();
  }
  function Ue(...O) {
    return a.value ? ce(() => Reflect.apply(a.value.t, null, [...O])) : ce(() => "");
  }
  function we(...O) {
    return a.value ? Reflect.apply(a.value.rt, null, [...O]) : "";
  }
  function We(...O) {
    return a.value ? ce(() => Reflect.apply(a.value.d, null, [...O])) : ce(() => "");
  }
  function $e(...O) {
    return a.value ? ce(() => Reflect.apply(a.value.n, null, [...O])) : ce(() => "");
  }
  function ae(O) {
    return a.value ? a.value.tm(O) : {};
  }
  function Ie(O, V) {
    return a.value ? a.value.te(O, V) : !1;
  }
  function Ve(O) {
    return a.value ? a.value.getLocaleMessage(O) : {};
  }
  function he(O, V) {
    a.value && (a.value.setLocaleMessage(O, V), _.value[O] = V);
  }
  function He(O, V) {
    a.value && a.value.mergeLocaleMessage(O, V);
  }
  function Ye(O) {
    return a.value ? a.value.getDateTimeFormat(O) : {};
  }
  function et(O, V) {
    a.value && (a.value.setDateTimeFormat(O, V), I.value[O] = V);
  }
  function Ge(O, V) {
    a.value && a.value.mergeDateTimeFormat(O, V);
  }
  function xe(O) {
    return a.value ? a.value.getNumberFormat(O) : {};
  }
  function pe(O, V) {
    a.value && (a.value.setNumberFormat(O, V), T.value[O] = V);
  }
  function Xe(O, V) {
    a.value && a.value.mergeNumberFormat(O, V);
  }
  const Ke = {
    get id() {
      return a.value ? a.value.id : -1;
    },
    locale: h,
    fallbackLocale: A,
    messages: C,
    datetimeFormats: B,
    numberFormats: te,
    get inheritLocale() {
      return a.value ? a.value.inheritLocale : s;
    },
    set inheritLocale(O) {
      a.value && (a.value.inheritLocale = O);
    },
    get availableLocales() {
      return a.value ? a.value.availableLocales : Object.keys(_.value);
    },
    get modifiers() {
      return a.value ? a.value.modifiers : d;
    },
    get pluralRules() {
      return a.value ? a.value.pluralRules : b;
    },
    get isGlobal() {
      return a.value ? a.value.isGlobal : !1;
    },
    get missingWarn() {
      return a.value ? a.value.missingWarn : g;
    },
    set missingWarn(O) {
      a.value && (a.value.missingWarn = O);
    },
    get fallbackWarn() {
      return a.value ? a.value.fallbackWarn : v;
    },
    set fallbackWarn(O) {
      a.value && (a.value.missingWarn = O);
    },
    get fallbackRoot() {
      return a.value ? a.value.fallbackRoot : S;
    },
    set fallbackRoot(O) {
      a.value && (a.value.fallbackRoot = O);
    },
    get fallbackFormat() {
      return a.value ? a.value.fallbackFormat : P;
    },
    set fallbackFormat(O) {
      a.value && (a.value.fallbackFormat = O);
    },
    get warnHtmlMessage() {
      return a.value ? a.value.warnHtmlMessage : U;
    },
    set warnHtmlMessage(O) {
      a.value && (a.value.warnHtmlMessage = O);
    },
    get escapeParameter() {
      return a.value ? a.value.escapeParameter : m;
    },
    set escapeParameter(O) {
      a.value && (a.value.escapeParameter = O);
    },
    t: Ue,
    getPostTranslationHandler: W,
    setPostTranslationHandler: Z,
    getMissingHandler: Fe,
    setMissingHandler: Me,
    rt: we,
    d: We,
    n: $e,
    tm: ae,
    te: Ie,
    getLocaleMessage: Ve,
    setLocaleMessage: he,
    mergeLocaleMessage: He,
    getDateTimeFormat: Ye,
    setDateTimeFormat: et,
    mergeDateTimeFormat: Ge,
    getNumberFormat: xe,
    setNumberFormat: pe,
    mergeNumberFormat: Xe
  };
  function je(O) {
    O.locale.value = i.value, O.fallbackLocale.value = u.value, Object.keys(_.value).forEach((V) => {
      O.mergeLocaleMessage(V, _.value[V]);
    }), Object.keys(I.value).forEach((V) => {
      O.mergeDateTimeFormat(V, I.value[V]);
    }), Object.keys(T.value).forEach((V) => {
      O.mergeNumberFormat(V, T.value[V]);
    }), O.escapeParameter = m, O.fallbackFormat = P, O.fallbackRoot = S, O.fallbackWarn = v, O.missingWarn = g, O.warnHtmlMessage = U;
  }
  return na(() => {
    if (e.proxy == null || e.proxy.$i18n == null)
      throw Q(J.NOT_AVAILABLE_COMPOSITION_IN_LEGACY);
    const O = a.value = e.proxy.$i18n.__composer;
    t === "global" ? (i.value = O.locale.value, u.value = O.fallbackLocale.value, _.value = O.messages.value, I.value = O.datetimeFormats.value, T.value = O.numberFormats.value) : l && je(O);
  }), Ke;
}
const fl = [
  "locale",
  "fallbackLocale",
  "availableLocales"
], dn = ["t", "rt", "d", "n", "tm", "te"];
function _l(e, t) {
  const n = /* @__PURE__ */ Object.create(null);
  return fl.forEach((l) => {
    const a = Object.getOwnPropertyDescriptor(t, l);
    if (!a)
      throw Q(J.UNEXPECTED_ERROR);
    const s = ea(a.value) ? {
      get() {
        return a.value.value;
      },
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      set(i) {
        a.value.value = i;
      }
    } : {
      get() {
        return a.get && a.get();
      }
    };
    Object.defineProperty(n, l, s);
  }), e.config.globalProperties.$i18n = n, dn.forEach((l) => {
    const a = Object.getOwnPropertyDescriptor(t, l);
    if (!a || !a.value)
      throw Q(J.UNEXPECTED_ERROR);
    Object.defineProperty(e.config.globalProperties, `$${l}`, a);
  }), () => {
    delete e.config.globalProperties.$i18n, dn.forEach((l) => {
      delete e.config.globalProperties[`$${l}`];
    });
  };
}
Yr();
__INTLIFY_JIT_COMPILATION__ ? Kt(Ur) : Kt(Mr);
Cr(sr);
Pr(Dn);
if (__INTLIFY_PROD_DEVTOOLS__) {
  const e = me();
  e.__INTLIFY__ = !0, Er(e.__INTLIFY_DEVTOOLS_GLOBAL_HOOK__);
}
export {
  El as c,
  Dt as u
};
