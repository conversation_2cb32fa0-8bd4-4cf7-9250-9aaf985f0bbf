/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    BookedDayDetailsTable: typeof import('./components/bookedDayDetailsTable.vue')['default']
    BookedWeekDetailsTable: typeof import('./components/bookedWeekDetailsTable.vue')['default']
    BookInstruments: typeof import('./components/BookInstruments.vue')['default']
    DeleteBookingDialog: typeof import('./components/DeleteBookingDialog.vue')['default']
    ElAutocomplete: typeof import('element-plus/es')['ElAutocomplete']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElTimeSelect: typeof import('element-plus/es')['ElTimeSelect']
    HelloWorld: typeof import('./components/HelloWorld.vue')['default']
    InFormContainer: typeof import('./components/in-form/InFormContainer.vue')['default']
    InFormDemo: typeof import('./components/in-form/InFormDemo.vue')['default']
    InFormLayout: typeof import('./components/in-form/InFormLayout.vue')['default']
    InstrumentBookingConfigDialog: typeof import('./components/InstrumentBookingConfigDialog.vue')['default']
    InstrumentBookingCreateDialog: typeof import('./components/InstrumentBookingCreateDialog.vue')['default']
    InstrumentsBookManage: typeof import('./components/instrumentsBookManage/instrumentsBookManage.vue')['default']
    InstrumentsBookMine: typeof import('./components/instrumentsBookMine/instrumentsBookMine.vue')['default']
    InstrumentsBookMineRightMenu: typeof import('./components/instrumentsBookMine/instrumentsBookMineRightMenu.vue')['default']
    InstrumentsBookMineToolBar: typeof import('./components/instrumentsBookMine/InstrumentsBookMineToolBar.vue')['default']
    Loading: typeof import('./components/loading/Loading.vue')['default']
    MenuItem: typeof import('./components/context-menu/MenuItem.vue')['default']
    ModalA: typeof import('./components/ModalA.vue')['default']
    ModalB: typeof import('./components/ModalB.vue')['default']
    OperationButton: typeof import('./components/in-form/operation-tool-bar/OperationButton.vue')['default']
    OperationToolBar: typeof import('./components/in-form/operation-tool-bar/OperationToolBar.vue')['default']
    TemplateGeneralSettings: typeof import('./components/template/TemplateGeneralSettings.vue')['default']
    TemplateHistoryDialog: typeof import('./components/template/TemplateHistoryDialog.vue')['default']
    TemplateTypeManager: typeof import('./components/template/TemplateTypeManager.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
