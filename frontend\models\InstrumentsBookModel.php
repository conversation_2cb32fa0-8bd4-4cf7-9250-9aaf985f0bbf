<?php

namespace frontend\models;

use Yii;

/**
 * This is the model class for table "instruments_book".
 *
 * @property string $id
 * @property integer $instrument_id
 * @property integer $create_by
 * @property string $create_time
 * @property integer $update_by
 * @property string $update_time
 * @property integer $status
 * @property string $start_time
 * @property string $end_time
 * @property string $related_experiment
 * @property string $remark
 * @property string $reminder
 */
class InstrumentsBookModel extends \frontend\core\CommonModel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'instruments_book';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['instrument_id', 'create_by', 'update_by', 'status', 'reminder'], 'integer'],
            [['create_time', 'update_time', 'start_time', 'end_time'], 'safe'],
            [['remark'], 'string'],
            [['related_experiment'], 'string', 'max' => 1000]
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'instrument_id' => 'Instrument ID',
            'create_by' => 'Create By',
            'create_time' => 'Create Time',
            'update_by' => 'Update By',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'start_time' => 'Start Time',
            'end_time' => 'End Time',
            'related_experiment' => 'Related Experiment',
            'remark' => 'Remark',
            'reminder' => 'reminder'
        ];
    }
}
