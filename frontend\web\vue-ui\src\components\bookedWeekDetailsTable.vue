<template>
  <div class="booked-details">
    <ElTable :data="tableData"
             border
             v-loading="bookedLoading"
             element-loading-custom-class="custom-loading"
             element-loading-background="rgba(255, 255, 255, 0.8)"
             style="width: calc(100vw - 72px); position: relative;"
             :header-cell-class-name="setHeaderClass"
             :cell-class-name="clearTimeBorder"
             v-cell-selection
             ref="bookingDetailTable"
             :cell-style="setCellColor"
             :header-row-class-name="'clear-header-border'"
             @cell-mouse-enter="handleCellEnter"
             @cell-mouse-leave="handleCellLeave"
             :row-height="100">
      <ElTableColumn fixed prop="instrumentName" width="270">
        <template #header>
          <ElInput
              v-model="searchInstruments"
              :placeholder="t('bookInstruments.searchInstrument')"
              clearable
              class="search-instruments"
              @keyup.enter="handlePrefixClick(false)"
          >
            <template #prefix>
              <!-- 自定义前置图标并绑定点击事件 -->
              <ElIcon @click="handlePrefixClick(false)">
                <Search/>
              </ElIcon>
            </template>
          </ElInput>
        </template>
        <template #default="scope">
          <ElPopover
              class="box-item"
              placement="right"
              transition="el-zoom-in-left"
              :show-arrow="false"
              width="352"
          >
            <template #reference>
              <div style="margin-left: 10px">
                <div class="instrument-name disable-text-select">{{ scope.row.instrumentName }}</div>
                <div class="instrument-code disable-text-select">{{ scope.row.instrumentCode }}</div>
              </div>
            </template>
            <div class="instruments-divider">
              <div class="instrument-name-pop">{{ scope.row.instrumentName }}</div>
              <div class="instrument-code-pop">{{ scope.row.instrumentCode }}</div>
              <ElDivider/>
              <ElDescriptions
                  :column="1"
              >
                <ElDescriptionsItem :label="t('bookInstruments.state')" class-name="popover-state-class">
                  {{ scope.row.state }}
                </ElDescriptionsItem>
                <ElDescriptionsItem :label="t('bookInstruments.responsible')">
                  {{ scope.row.responsiblePerson }}
                </ElDescriptionsItem>
                <ElDescriptionsItem :label="t('bookInstruments.position')" :span="2">
                  {{ scope.row.position }}
                </ElDescriptionsItem>
                <ElDescriptionsItem :label="t('bookInstruments.bookableTime')" :span="2">
                  {{ scope.row.bookableTime.join(',') }}
                </ElDescriptionsItem>
                <ElDescriptionsItem :label="t('bookInstruments.singleTimeLimit')" :span="2">
                  {{ scope.row.singleTimeLimit }}
                </ElDescriptionsItem>
                <ElDescriptionsItem :label="t('bookInstruments.advance')" :span="2">
                  {{ scope.row.advance }}
                </ElDescriptionsItem>
              </ElDescriptions>
            </div>
            <img :src=scope.row.picture>
          </ElPopover>
        </template>
      </ElTableColumn>

      <ElTableColumn
          v-for="column in columns"
          :key="column.key"
          :prop="column.prop"
          :label="column.label"
          width="35"
          :resizable="false"
      >
        <template #header>
          <div>{{ column.date ?? '' }} {{ column.label }}</div>
          <div
              v-if="showTimeLine(column.prop, 1)"
              class="red-dot"
              :style="redDotStyle(column.prop)"
          >
          </div>
        </template>
        <template #default="{row}">
            <div class="blankCell" @click="handleCellClick(row, column.prop, $event)">
              <div class="cell-tips disable-text-select" :style="getCellTipsStyle(row, column)">{{
                  row[column.prop]
                }}
              </div>
              <div class="red-timeline-offset red-timeline" :style="redLineStyle"
                   v-if="showTimeLine(column.prop, 0)"></div>
            </div>
        </template>
      </ElTableColumn>
    </ElTable>
    <ElPagination
      :page-sizes="[10, 15, 25, 50]"
      :page-size="pageSize"
      :total="total"
      layout="sizes, prev, slot, next, total"
      :pager-count="2"
      style="margin-left: 7px"
      @size-change="handleSizeChange"
      @prev-click="curPageChange"
      @next-click="curPageChange"
    >
      <template #default>
        <span style="color: #7366FF">{{ currentPage }}</span>/{{ Math.ceil(total / pageSize) }}
      </template>
    </ElPagination>
    <ElTooltip
        class="box-item"
        effect="dark"
        :visible="tooltipsVisible"
        placement="top"
        trigger="hover"
        virtual-triggering
        :virtual-ref="tooltipsTarget"
    >
      <template #content>
        <div v-html="tooltipsHtml"></div>  <!-- 通过插槽隔离 HTML 内容 -->
      </template>
    </ElTooltip>
    <ElPopover
        :visible="popoverVisible"
        :width="400"
        trigger="click"
        placement="right"
        :virtual-ref="popoverTarget"
        virtual-triggering
        :show-arrow="false"
    >
      <div class="instruments-divider booking-info" v-if="popoverData[currentPopover - 1]">
        <div class="flex-space">
          <div class="instrument-name-pop">{{ popoverData[currentPopover - 1].experimenter }}的预约</div>
          <div class="top-icon">
            <ElIcon v-if="popoverData[currentPopover - 1].updateShow">
              <Edit @click="openEditDialog(popoverData[currentPopover - 1])"/>
            </ElIcon>
            <ElIcon v-if="popoverData[currentPopover - 1].deleteShow">
              <Delete @click="deleteInstrumentRecord(popoverData[currentPopover - 1])"/>
            </ElIcon>
          </div>
        </div>

        <div class="instrument-code-pop">{{ popoverData[currentPopover - 1].instrument }}</div>
        <ElDivider/>
        <ElDescriptions
            :column="1"
        >
          <ElDescriptionsItem v-if="popoverData[currentPopover-1].bookedTime">
            <ElIcon class="icon-align">
              <Calendar/>
            </ElIcon>
            {{ popoverData[currentPopover - 1].bookedTime.join(' - ') }}
          </ElDescriptionsItem>
          <ElDescriptionsItem v-if="popoverData[currentPopover-1].reminder">
            <ElIcon class="icon-align">
              <AlarmClock/>
            </ElIcon>
            {{ popoverData[currentPopover - 1].reminder }}
          </ElDescriptionsItem>
          <ElDescriptionsItem v-if="popoverData[currentPopover-1].ELNPage">
            <ElIcon class="icon-align">
              <Link/>
            </ElIcon>
            <div style="display: inline-block">
              <div class="eln-page" v-for="page in popoverData[currentPopover-1].ELNPage" :key="page">{{ page }}</div>
            </div>
          </ElDescriptionsItem>
          <ElDescriptionsItem v-if="popoverData[currentPopover-1].remark">
            <span class="icon-align">
              <svg width="13" height="13" viewBox="0 0 13.2 13.2" fill="none"
                   xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <defs/>
                <path id="路径（边框）"
                      d="M6.6 0C9.33 0 11.26 1.93 11.26 1.93C13.2 3.86 13.2 6.59 13.2 6.59C13.2 9.33 11.26 11.26 11.26 11.26C9.33 13.2 6.6 13.2 6.6 13.2C3.86 13.2 1.93 11.26 1.93 11.26C0 9.33 0 6.59 0 6.59C0 3.86 1.93 1.93 1.93 1.93C3.86 0 6.6 0 6.6 0ZM2.78 2.78C2.78 2.78 4.36 1.19 6.6 1.19C6.6 1.19 8.83 1.19 10.41 2.78C10.41 2.78 12 4.36 12 6.59C12 6.59 12 8.83 10.41 10.41C10.41 10.41 8.83 12 6.6 12C6.6 12 4.36 12 2.78 10.41C2.78 10.41 1.19 8.83 1.19 6.59C1.19 6.59 1.19 4.36 2.78 2.78ZM6.6 3.6C6.27 3.6 6 3.86 6 4.2C6 4.53 6.26 4.8 6.6 4.8C6.93 4.8 7.2 4.53 7.2 4.2C7.2 4.04 7.14 3.88 7.03 3.77C6.91 3.66 6.76 3.6 6.6 3.6ZM6 6.59C6 6.26 6.26 6 6.6 6C6.93 6 7.2 6.26 7.2 6.59L7.2 8.99C7.2 9.33 6.93 9.59 6.6 9.59C6.26 9.59 6 9.33 6 8.99L6 6.59Z"
                      fill="#B6B6BF" fill-opacity="1.000000" fill-rule="evenodd"/>
              </svg>
            </span>
            {{ popoverData[currentPopover - 1]?.remark }}
          </ElDescriptionsItem>
        </ElDescriptions>
        <ElPagination
            :page-size="1"
            :total="totalPopover"
            layout="prev, slot, next"
            style="margin-left: 7px"
            @prev-click="curPopoverChange"
            @next-click="curPopoverChange"
        >
          <template #default>
            <span style="color: #7366FF">{{ currentPopover }}</span>/{{ totalPopover }}
          </template>
        </ElPagination>
      </div>
    </ElPopover>
    <!-- 删除确认对话框 -->
    <DeleteBookingDialog
        :visible="deleteDialogVisible"
        :booking-info="deleteBookingInfo"
        :loading="deleteLoading"
        @confirm="handleDeleteConfirm(deleteBookingInfo.bookingId)"
        @cancel="handleDeleteCancel"
    />
  </div>
</template>

<script setup lang="ts">
import {AlarmClock, Calendar, Delete, Edit, Link, Search} from '@element-plus/icons-vue'
import {
  computed,
  reactive,
  ref,
  shallowRef,
  defineProps,
  watch,
  defineExpose,
  PropType,
  defineEmits,
  onMounted, onUnmounted
} from 'vue'
import axios from 'axios'
import 'element-plus/theme-chalk/base.css'
import { BookDetailsTable, BookingItem } from '../interface/types'
import dayjs from 'dayjs'
import { useI18n } from 'vue-i18n'
import {
  ElTable, ElTableColumn, ElTooltip, ElPagination, ElMessage,
  ElInput, ElIcon, ElPopover, ElDivider, ElDescriptions, ElDescriptionsItem
} from 'element-plus'
import DeleteBookingDialog from "@/components/DeleteBookingDialog.vue";
// Popover相关状态
const popoverVisible = ref(false)
const popoverTarget = ref()
const popoverData = ref<any>({})
const currentPopover = ref<number>(1)
const totalPopover = ref<number>(0)
// 删除确认对话框状态
const deleteDialogVisible = ref(false)
const deleteBookingInfo = ref<any>(null)
const deleteLoading = ref(false)
// tooltip相关状态
const tooltipsVisible = ref(false)
const tooltipsTarget = ref()
const tooltipsHtml = ref()
const { t } = useI18n()
const bookedLoading = ref(false)
const searchInstruments = ref<string>()
const currentPage = ref<number>(1)
const timeNow = ref<Date>(new Date())
const pageSize = ref<number>(15)
const total = ref<number>(0)
const bookingDetailTable = ref<InstanceType<typeof ElTable>>()
const selectedCells = ref(new Set())
let hoverRow = -1 // 记录悬停的行索引
const curClickRow = ref<any>({})
let afterDeleteClose = false
const hoverColumn = shallowRef(new Set())// 记录悬停的列属性
const emit = defineEmits(['bookedLoadingChange', 'openEdit', 'openCreate'])
const color = reactive({
  forbid: 'rgba(242, 242, 245, 0.5)',
  otherBooked: ['rgba(223, 223, 230, 0.5)', 'rgb(223, 223, 230)'],
  myBooked: ['rgba(224, 224, 255, 0.5)', 'rgb(224, 224, 255)'],
  white: ['#FFFFFF', 'rgba(115, 102, 255, 0.15)']
})
const weeks = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun']

// 使用国际化的星期标签
const getWeekLabel = (day) => {
  const weekLabels = {
    'mon': t('bookInstruments.monday'),
    'tue': t('bookInstruments.tuesday'),
    'wed': t('bookInstruments.wednesday'),
    'thu': t('bookInstruments.thursday'),
    'fri': t('bookInstruments.friday'),
    'sat': t('bookInstruments.saturday'),
    'sun': t('bookInstruments.sunday')
  }
  return weekLabels[day] || ''
}
const columns = reactive([
  {
    prop: 'mon0',
    label: getWeekLabel('mon'),
    key: 0,
    date: ''
  }, {
    prop: 'mon1',
    label: '',
    key: 0
  }, {
    prop: 'mon2',
    label: '',
    key: 0
  },
  {
    prop: 'mon3',
    label: '',
    key: 0
  }, {
    prop: 'mon4',
    label: '',
    key: 0
  }, {
    prop: 'mon5',
    label: '',
    key: 0
  },
  {
    prop: 'mon6',
    label: '',
    key: 0
  }, {
    prop: 'mon7',
    label: '',
    key: 0
  }, {
    prop: 'mon8',
    label: '',
    key: 0
  },
  {
    prop: 'mon9',
    label: '',
    key: 0
  }, {
    prop: 'mon10',
    label: '',
    key: 0
  }, {
    prop: 'mon11',
    label: '',
    key: 0
  },
  {
    prop: 'tue0',
    label: getWeekLabel('tue'),
    key: 0,
    date: ''
  }, {
    prop: 'tue1',
    label: '',
    key: 0
  }, {
    prop: 'tue2',
    label: '',
    key: 0
  },
  {
    prop: 'tue3',
    label: '',
    key: 0
  }, {
    prop: 'tue4',
    label: '',
    key: 0
  }, {
    prop: 'tue5',
    label: '',
    key: 0
  },
  {
    prop: 'tue6',
    label: '',
    key: 0
  }, {
    prop: 'tue7',
    label: '',
    key: 0
  }, {
    prop: 'tue8',
    label: '',
    key: 0
  },
  {
    prop: 'tue9',
    label: '',
    key: 0
  }, {
    prop: 'tue10',
    label: '',
    key: 0
  }, {
    prop: 'tue11',
    label: '',
    key: 0
  },
  {
    prop: 'wed0',
    label: getWeekLabel('wed'),
    key: 0,
    date: ''
  }, {
    prop: 'wed1',
    label: '',
    key: 0
  }, {
    prop: 'wed2',
    label: '',
    key: 0
  },
  {
    prop: 'wed3',
    label: '',
    key: 0
  }, {
    prop: 'wed4',
    label: '',
    key: 0
  }, {
    prop: 'wed5',
    label: '',
    key: 0
  },
  {
    prop: 'wed6',
    label: '',
    key: 0
  }, {
    prop: 'wed7',
    label: '',
    key: 0
  }, {
    prop: 'wed8',
    label: '',
    key: 0
  },
  {
    prop: 'wed9',
    label: '',
    key: 0
  }, {
    prop: 'wed10',
    label: '',
    key: 0
  }, {
    prop: 'wed11',
    label: '',
    key: 0
  },
  {
    prop: 'thu0',
    label: getWeekLabel('thu'),
    key: 0,
    date: ''
  }, {
    prop: 'thu1',
    label: '',
    key: 0
  }, {
    prop: 'thu2',
    label: '',
    key: 0
  },
  {
    prop: 'thu3',
    label: '',
    key: 0
  }, {
    prop: 'thu4',
    label: '',
    key: 0
  }, {
    prop: 'thu5',
    label: '',
    key: 0
  },
  {
    prop: 'thu6',
    label: '',
    key: 0
  }, {
    prop: 'thu7',
    label: '',
    key: 0
  }, {
    prop: 'thu8',
    label: '',
    key: 0
  },
  {
    prop: 'thu9',
    label: '',
    key: 0
  }, {
    prop: 'thu10',
    label: '',
    key: 0
  }, {
    prop: 'thu11',
    label: '',
    key: 0
  },
  {
    prop: 'fri0',
    label:  getWeekLabel('fri'),
    key: 0,
    date: ''
  }, {
    prop: 'fri1',
    label: '',
    key: 0
  }, {
    prop: 'fri2',
    label: '',
    key: 0
  },
  {
    prop: 'fri3',
    label: '',
    key: 0
  }, {
    prop: 'fri4',
    label: '',
    key: 0
  }, {
    prop: 'fri5',
    label: '',
    key: 0
  },
  {
    prop: 'fri6',
    label: '',
    key: 0
  }, {
    prop: 'fri7',
    label: '',
    key: 0
  }, {
    prop: 'fri8',
    label: '',
    key: 0
  },
  {
    prop: 'fri9',
    label: '',
    key: 0
  }, {
    prop: 'fri10',
    label: '',
    key: 0
  }, {
    prop: 'fri11',
    label: '',
    key: 0
  },
  {
    prop: 'sat0',
    label: getWeekLabel('sat'),
    key: 0,
    date: ''
  }, {
    prop: 'sat1',
    label: '',
    key: 0
  }, {
    prop: 'sat2',
    label: '',
    key: 0
  },
  {
    prop: 'sat3',
    label: '',
    key: 0
  }, {
    prop: 'sat4',
    label: '',
    key: 0
  }, {
    prop: 'sat5',
    label: '',
    key: 0
  },
  {
    prop: 'sat6',
    label: '',
    key: 0
  }, {
    prop: 'sat7',
    label: '',
    key: 0
  }, {
    prop: 'sat8',
    label: '',
    key: 0
  },
  {
    prop: 'sat9',
    label: '',
    key: 0
  }, {
    prop: 'sat10',
    label: '',
    key: 0
  }, {
    prop: 'sat11',
    label: '',
    key: 0
  },
  {
    prop: 'sun0',
    label: getWeekLabel('sun'),
    key: 0,
    date: ''
  }, {
    prop: 'sun1',
    label: '',
    key: 0
  }, {
    prop: 'sun2',
    label: '',
    key: 0
  },
  {
    prop: 'sun3',
    label: '',
    key: 0
  }, {
    prop: 'sun4',
    label: '',
    key: 0
  }, {
    prop: 'sun5',
    label: '',
    key: 0
  },
  {
    prop: 'sun6',
    label: '',
    key: 0
  }, {
    prop: 'sun7',
    label: '',
    key: 0
  }, {
    prop: 'sun8',
    label: '',
    key: 0
  },
  {
    prop: 'sun9',
    label: '',
    key: 0
  }, {
    prop: 'sun10',
    label: '',
    key: 0
  }, {
    prop: 'sun11',
    label: '',
    key: 0
  }
])
const tableData = ref<BookDetailsTable[]>([])
const props = defineProps({
  weekDate: {
    type: Array as PropType<string[]>,
    required: true
  },
  onlyBooked: {
    type: Boolean,
    required: true
  }
})

const mergeIntervals = (intervals: number[][]) => {
  if (intervals.length <= 1) return intervals

  // 按区间起始点升序排序
  intervals.sort((a, b) => a[0] - b[0])

  const merged = [intervals[0]]

  for (let i = 1; i < intervals.length; i++) {
    const current = intervals[i]
    const lastMerged = merged[merged.length - 1]

    if (current[0] <= lastMerged[1]) {
      // 合并重叠区间
      lastMerged[1] = Math.max(lastMerged[1], current[1])
    } else {
      // 不重叠，直接加入
      merged.push(current)
    }
  }

  return merged
}

const mergeBooked = (intervals: any) => {
  if (intervals.length <= 1) return intervals

  // 按区间起始点升序排序
  intervals.sort((a: any, b: any) => a.size[0] - b.size[0])

  const merged = [intervals[0]]

  for (let i = 1; i < intervals.length; i++) {
    const current = intervals[i]
    const lastMerged = merged[merged.length - 1]

    if (current.size[0] <= lastMerged.size[1]) {
      // 合并重叠区间
      lastMerged.size[1] = Math.max(lastMerged.size[1], current.size[1])
    } else {
      // 不重叠，直接加入
      merged.push(current)
    }
  }

  return merged
}

const handlePrefixClick = async (booked:boolean) => {
  bookedLoading.value = true
  document.querySelectorAll('.el-table__cell').forEach(cell => {
    cell.classList.toggle('selected-cell', false)
  })
  emit('bookedLoadingChange', bookedLoading.value)
  const param = searchInstruments.value
  const url = '/?r=instrument-booking/get-instruments-data'
  const res = await axios.post(url, {
    instrumentParam: param,
    bookTime: [props.weekDate[0], props.weekDate[6]],
    lang: (window as any).lang,
    pageSize: pageSize.value,
    curPage: currentPage.value,
    onlyBooked: booked ? !props.onlyBooked : props.onlyBooked,
    headers: {
      Accept: 'application/json',
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  total.value = res.data.data.totalCount
  tableData.value = res.data.data.instruments
  let index = 0
  tableData.value.forEach(item => {
    item.cellMap = new Map()
    item.cellEvents = {}
    item.rowIndex = index++
    if (item.bookableTime && item.bookableTime.length > 0) {
      item.bookableTime.forEach(time => {
        getWhiteCellMap(time, item)
      })
    } else {
      for (let i = 0; i < 84; i++) {
        // 白色，空白事件（点击弹出预约框）
        item.cellEvents[columns[i].prop] = {
          events: new Set(),
          includedItems: [],
          bookedCellName: []
        }
        item.cellEvents[columns[i].prop].events.add('white')
        item.cellMap.set(columns[i].prop, {
          forbid: '0% 100%',
          white: [[0, 100]],
          booked: []
        })
      }
    }
    item.bookedInfo?.forEach(bookedInfo => {
      const startDate = getDateYMD(new Date(bookedInfo.bookedTime[0]))
      const endDate = getDateYMD(new Date(bookedInfo.bookedTime[1]))
      const startStr = startDate < props.weekDate[0] ? props.weekDate[0] + ' 00:00' : bookedInfo.bookedTime[0]
      const endStr = endDate > props.weekDate[6] ? props.weekDate[6] + ' 23:59' : bookedInfo.bookedTime[1]
      if(endStr > startStr) {
        getBookedCellMap([startStr, endStr], item, bookedInfo)
      }
    })
    item.cellMap.forEach((value: any, key: any) => {
      value.booked = mergeBooked(value.booked)
      value.white = mergeIntervals(value.white)
      setCellStyleProperty(value)
    })
  })
  bookedLoading.value = false
  emit('bookedLoadingChange', bookedLoading.value) // 向父组件发送事件，传递 false 值
}

// 点击其他地方关闭popover
const handleDocumentClick = (event: MouseEvent) => {
  // 如果点击的不是popover内容，则关闭popover
  const target = event.target as HTMLElement
  if (!target.closest('.el-popover') && !target.closest('.cell-content') && !target.closest('.delete-booking-dialog')) {
    popoverVisible.value = false
  }
}

const handleSizeChange = (val:number) => {
  pageSize.value = val
  handlePrefixClick(false)
}

const curPageChange = (val:number) => {
  currentPage.value = val
  handlePrefixClick(false)
}
const curPopoverChange = (val: number) => {
  currentPopover.value = val
}
const setCellStyleProperty = (cellColor) => {
  const finalColor = []
  const whiteStart = cellColor.white?.[0]?.[0] ?? 100
  const bookedStart = cellColor.booked?.[0]?.size[0]
  const minStart = Math.min(whiteStart, bookedStart ?? 100)
  for (let i = 0; i < 2; i++) {
    if (minStart > 0) {
      finalColor.push(color.forbid + ' 0% ' + minStart + '%')
    }
    if (bookedStart && whiteStart < bookedStart) {
      finalColor.push(color.white[0] + ' ' + whiteStart + '% ' + bookedStart + '%')
    }
    for (let j = 0; j < cellColor.booked.length; j++) {
      const item = cellColor.booked[j]
      finalColor.push(color[item.color][i] + ' ' + item.size.join('% ') + '%')
      // 填充空白
      if (j < cellColor.booked.length - 1) {
        finalColor.push(color.white[0] + ' ' + item.size[1] + '% ' + cellColor.booked[j + 1].size[0] + '%')
      }
    }
    cellColor.white.forEach(item => {
      finalColor.push(color.white[0] + ' ' + item.join('% ') + '%')
    })
    finalColor.push(color.forbid + ' ' + cellColor.forbid)
    if (i === 0) {
      cellColor.final = `linear-gradient(90deg, ${finalColor.join(', ')})`
      finalColor.length = 0
    } else {
      cellColor.hoverFinal = `linear-gradient(90deg, ${finalColor.join(', ')})`
    }
  }
}
const initialCell = (item: BookDetailsTable, cellName: string) => {
  const map = item.cellMap
  map.set(cellName, {
    forbid: '0% 100%',
    white: [],
    booked: []
  })
  // 初始化当前单元格所需事件的set,以及当前单元格所包含的预约事件有哪些，是否为头
  item.cellEvents[cellName] = {
    events: new Set(),
    includedItems: [],
    bookedCellName: []
  }
}
const getWhiteCellMap = (time: string, item: BookDetailsTable) => {
  const timeArr = time.split('-')
  const startStr = timeArr[0]
  const endStr = timeArr[1]
  const startArr = startStr.split(':').map(o => Number(o))
  const endArr = endStr.split(':').map(o => Number(o))
  startArr[0] += startArr[1] / 60
  endArr[0] += startArr[1] / 60
  const map = item.cellMap
  let start, end, ratio

  // 解决一些预约时间正好卡在一个两小时段内的情况
  if (endArr[0] - startArr[0] < 2) {
    if (endArr[0] % 2 > startArr[0] % 2) {
      // 如果周一没有，那么周二-日肯定也没有
      const white = map.get('mon' + Math.floor(startArr[0] / 2))
      if (!white) {
        weeks.forEach(week => {
          initialCell(item, week + Math.floor(startArr[0] / 2))
        })
      }
      const startRatio = Math.round((startArr[0] % 2 / 2 * 100))
      const endRatio = Math.round((endArr[0] % 2 / 2 * 100))
      weeks.forEach(week => {
        item.cellEvents[week + Math.floor(startArr[0] / 2)].events.add('white')
        map.get(week + Math.floor(startArr[0] / 2)).white.push([startRatio, endRatio])
      })
    }
  } else {
    // 预约事件的时间大于两小时的情况
    start = Math.floor(startArr[0] / 2)
    if (!map.get('mon' + start)) {
      weeks.forEach(week => {
        initialCell(item, week + start)
      })
    }
    ratio = Math.round((startArr[0] % 2 / 2 * 100))
    if (ratio !== 100) {
      // 白色，空白事件（点击弹出预约框）
      if (ratio === 0) {
        weeks.forEach(week => {
          item.cellEvents[week + start].events.add('white')
        })
      }
      weeks.forEach(week => {
        map.get(week + start).white.push([ratio, 100])
      })
    }
    end = Math.floor(endArr[0] / 2)
    if (!map.get('mon' + end)) {
      weeks.forEach(week => {
        initialCell(item, week + end)
      })
    }
    ratio = Math.round((endArr[0] % 2 / 2 * 100))
    if (ratio !== 0) {
      // 白色，空白事件（点击弹出预约框）
      if (ratio === 100) {
        weeks.forEach(week => {
          item.cellEvents[week + end].events.add('white')
        })
      }
      weeks.forEach(week => {
        map.get(week + end).white.push([0, ratio])
      })
    }
    for (let i = start + 1; i < end; i++) {
      if (!map.get('mon' + i)) {
        weeks.forEach(week => {
          initialCell(item, week + i)
        })
      }
      // 白色，空白事件（点击弹出预约框）
      weeks.forEach(week => {
        item.cellEvents[week + i].events.add('white')
        map.get(week + i).white.push([0, 100])
      })
    }
  }
}
// 获取当前星期的方法 星期一 ：0 星期二：1 ... 星期日：6
const getWeek = (day: dayjs) => {
  let res = day.day()
  if (res === 0) {
    res = 6
  } else {
    res--
  }
  return res
}
const getBookedCellMap = (time: string[], item: BookDetailsTable, bookedInfo: BookingItem) => {
  const startStr = dayjs(time[0])
  const endStr = dayjs(time[1])
  // 开始和结束时间之间的分钟差
  const diffMinutes = endStr.diff(startStr, 'minute')
  const startWeek = getWeek(startStr)
  const endWeek = getWeek(endStr)
  const map = item.cellMap
  const cellName: any[] = []
  const start = (startStr.hour() * 60 + startStr.minute()) / 60
  const end = (endStr.hour() * 60 + endStr.minute()) / 60
  const startWeekIndex = startWeek * 12 + Math.floor(start / 2)
  const endWeekIndex = endWeek * 12 + Math.floor(end / 2)
  const startRatio = Math.round((start % 2 / 2 * 100))
  const endRatio = Math.round((end % 2 / 2 * 100))
  if (!map.get(columns[startWeekIndex].prop)) {
    initialCell(item, columns[startWeekIndex].prop)
  }
  // 解决一些预约时间正好在同一个两小时段的情况
  if (diffMinutes < 120 && (end % 2 > start % 2)) {
    // 根据日期和时间算出当前的格子index，根据index获取格子prop名称
    // 需要预定事件（点击给出预定信息
    item.cellEvents[columns[startWeekIndex].prop].events.add('booked')
    item.cellEvents[columns[startWeekIndex].prop].includedItems.push(bookedInfo)
    cellName.push(columns[startWeekIndex].prop)
    const color = bookedInfo.currentBooked ? 'myBooked' : 'otherBooked'
    map.get(columns[startWeekIndex].prop).booked.push({
      color: color,
      size: [startRatio, endRatio]
    })
  } else {
    // 预约事件的时间跨过两个格子的情况
    if (startRatio !== 100) {
      // 渲染开始块的颜色
      item.cellEvents[columns[startWeekIndex].prop].events.add('booked')
      item.cellEvents[columns[startWeekIndex].prop].includedItems.push(bookedInfo)
      cellName.push(columns[startWeekIndex].prop)
      const color = bookedInfo.currentBooked ? 'myBooked' : 'otherBooked'
      map.get(columns[startWeekIndex].prop).booked.push({
        color: color,
        size: [startRatio, 100]
      })
    }
    // 渲染结束块的颜色
    if (!map.get(columns[endWeekIndex].prop)) {
      initialCell(item, columns[endWeekIndex].prop)
    }
    if (endRatio !== 0) {
      // 需要预定事件（点击给出预定信息
      item.cellEvents[columns[endWeekIndex].prop].events.add('booked')
      item.cellEvents[columns[endWeekIndex].prop].includedItems.push(bookedInfo)
      cellName.push(columns[endWeekIndex].prop)
      const color = bookedInfo.currentBooked ? 'myBooked' : 'otherBooked'
      map.get(columns[endWeekIndex].prop).booked.push({
        color: color,
        size: [0, endRatio]
      })
    }
    for (let i = startWeekIndex + 1; i < endWeekIndex; i++) {
      if (!map.get(columns[i].prop)) {
        initialCell(item, columns[i].prop)
      }
      // 需要预定事件（点击给出预定信息
      item.cellEvents[columns[i].prop].events.add('booked')
      item.cellEvents[columns[i].prop].includedItems.push(bookedInfo)
      cellName.push(columns[i].prop)
      const color = bookedInfo.currentBooked ? 'myBooked' : 'otherBooked'
      map.get(columns[i].prop).booked.push({
        color: color,
        size: [0, 100]
      })
    }
    // 表格添加预定信息
    map.get(columns[startWeekIndex + 1].prop).left = 95 - startRatio
    map.get(columns[startWeekIndex + 1].prop).width = diffMinutes / 120 * 35
    if (diffMinutes < 120) {
      item[columns[startWeekIndex + 1].prop] = ''
    } else {
      item[columns[startWeekIndex + 1].prop] = bookedInfo.experimenter + ' ' + bookedInfo.bookedTime.join(' - ') + ' 备注：' + bookedInfo.remark
    }
  }
  if (cellName.length > 0) {
    cellName.forEach(cell => {
      item.cellEvents[cell].bookedCellName.push(cellName)
    })
  }
}

const setHeaderClass = ({ column }) => {
  if (column.property === 'instrumentName') {
    return 'clear-header-border'
  } else {
    const originalString = column.property
    const trimmedString = originalString.slice(3)
    const numberValue = Number(trimmedString)
    let final = ''
    if (numberValue === 0) {
      final += 'header-time-class'
    } else {
      final += 'clear-border-right'
    }
    return final
  }
}

const clearTimeBorder = ({
                           column,
                           rowIndex
                         }) => {
  // 根据条件返回类名
  let className = ''
  if (column.property !== 'instrumentName') {
    className += ' default-cell-color'
    const originalString = column.property
    const trimmedString = originalString.slice(3)
    const numberValue = Number(trimmedString)
    if (numberValue !== 11) {
      className += ' clear-border-right'
    }
  }
  return className
}

const setCellColor = ({
                        row,
                        column,
                        rowIndex
                      }) => {
  const temp = hoverColumn.value
  const cellColor = row.cellMap?.get(column.property)
  const resColor = {}
  if (row.cellMap?.get(column.property)?.width) {
    resColor.zIndex = 1
  }
  if (cellColor) {
    const bookedHover = rowIndex === hoverRow && temp.has(column.property) ? 1 : 0
    if (bookedHover) {
      resColor.background = cellColor.hoverFinal
    } else {
      resColor.background = cellColor.final
    }
  }
  return resColor
}
// 处理单元格点击后的popover显隐
const handleCellClick = (row: any, cellName: string, event: MouseEvent) => {
  const reservations = row.cellEvents?.[cellName]?.includedItems
  // 阻止事件冒泡
  event.stopPropagation()
  popoverData.value = reservations ?? []
  totalPopover.value = reservations ? reservations.length : 0
  currentPopover.value = 1
  if (reservations && reservations.length > 0) {
    curClickRow.value = {
      instrumentName: row.instrumentName,
      instrumentId: row.instrumentId,
      available_slots: row.available_slots,
      max_advance_day: row.max_advance_day,
      min_advance: row.min_advance,
      max_booking_duration: row.max_booking_duration
    }
    // 设置虚拟触发元素
    popoverTarget.value = event.target
    // 显示popover
    popoverVisible.value = true
    tooltipsVisible.value = false
  } else {
    curClickRow.value = {}
    popoverVisible.value = false
  }
}
const handleCellEnter = (row: any, column: any, cell: HTMLTableCellElement) => {
  // 获取行索引
  hoverRow = row.rowIndex
  // 查看该格子是否是空白格子，若是则给出指定列名
  const events = row.cellEvents?.[column.property]?.events
  if (events && events.values().next().value === 'white' && events.size === 1) {
    cell.style.background = color.white[1]
    return
  }
  // 查看该格子是否有预定，若有，给出该格子所占预约的所有格子背景色
  if (row.cellEvents?.[column.property]?.bookedCellName && row.cellEvents?.[column.property]?.bookedCellName.length > 0) {
    hoverColumn.value = new Set(row.cellEvents[column.property].bookedCellName.flat(2)) // 获取列属性
  }
  const reservations = row.cellEvents?.[column.property]?.includedItems
  if (reservations?.length > 0) {
    let finalStr = ''
    reservations?.forEach(item => {
      finalStr += item.experimenter + ' ' + item.bookedTime.join(' - ') + ' 备注：' + item.remark + '<br/>'
    })
    tooltipsHtml.value = finalStr
    tooltipsTarget.value = cell
    tooltipsVisible.value = true
  } else {
    tooltipsVisible.value = false
  }
}

const handleCellLeave = (row: any, column: any, cell: HTMLTableCellElement) => {
  // 清空，下次hover重新赋值
  hoverRow = -1
  if (cell.style.background === color.white[1]) {
    cell.style.background = color.white[0]
  }
  if (hoverColumn.value.size > 0) {
    hoverColumn.value = new Set()
  }
  tooltipsVisible.value = false
}

const openEditDialog = (bookedInfo: any) => {
  const row = curClickRow.value
  const data = {
    name: row.instrumentName,
    instrument_id: row.instrumentId,
    id: bookedInfo.bookingId,
    start_time: bookedInfo.bookedTime[0],
    end_time: bookedInfo.bookedTime[1],
    source: 'bookInstruments',
    warn: bookedInfo.warn,
    related_experiment: bookedInfo.ELNPage?.join(','),
    remark: bookedInfo.remark,
    available_slots: Array.isArray(row.available_slots) ? row.available_slots : JSON.parse(row.available_slots || '[]'),
    max_advance_day: row.max_advance_day,
    min_advance: JSON.parse(row.min_advance || '{"value":"","unit":""}'),
    max_booking_duration: JSON.parse(row.max_booking_duration || '{"value":"","unit":""}')
  }
  emit('openEdit', data)
}

const getCellTipsStyle = (row: any, column: any) => {
  if (row.cellMap?.get(column.prop)?.width) {
    const width = row.cellMap.get(column.prop)?.width
    const left = -row.cellMap.get(column.prop)?.left
    return {
      width: width + 'px',
      left: left + '%'
    }
  }
  return {}
}
// 自定义指令
const vCellSelection = {
  mounted (el) {
    let isDragging = false
    let startCell = null

    el.addEventListener('mousedown', (e) => {
      document.querySelectorAll('.el-table__cell').forEach(cell => {
        cell.classList.toggle('selected-cell', false)
      })
      const cell = e.target.closest('.el-table__cell')
      if (cell) {
        startCell = getCellPosition(cell)
        if (selectedCells.value.has(`${startCell.rowIndex}-${startCell.colIndex}`)) {
          const colIndexes = Array.from(selectedCells.value).map(cell => {
            return parseInt(cell.split('-')[1])
          })
          // 计算最大值和最小值
          const minColIndex = Math.min(...colIndexes)
          const maxColIndex = Math.max(...colIndexes)
          const time = [getTime(minColIndex-1), getTime(maxColIndex)]
          const row = tableData.value[startCell.rowIndex]
          const data = {
            name: row.instrumentName,
            id: row.instrumentId,
            time: time,
            related_experiment: '',
            remark: '',
            source: 'bookInstruments',
            available_slots: Array.isArray(row.available_slots) ? row.available_slots : JSON.parse(row.available_slots || '[]'),
            max_advance_day: row.max_advance_day,
            min_advance: JSON.parse(row.min_advance || '{"value":"","unit":""}'),
            max_booking_duration: JSON.parse(row.max_booking_duration || '{"value":"","unit":""}')
          }
          emit('openCreate', data)
          selectedCells.value.clear()
          return
        }  else {
          selectedCells.value.clear()
        }
        isDragging = true
        const cellsInRange = calculateRange(startCell, startCell)
        highlightCells(cellsInRange, startCell.rowIndex)
        el.addEventListener('mousemove', handleMouseMove)
        el.addEventListener('mouseup', handleRelease)
        // e.preventDefault()
      } else {
        selectedCells.value.clear()
      }
    })

    const handleMouseMove = (e) => {
      if (!isDragging) return
      const currentCell = document.elementFromPoint(e.clientX, e.clientY)
      // 点击后切换鼠标图标
      if (currentCell.closest('.el-table__row')) {
        currentCell.closest('.el-table__row').style.cursor = 'e-resize'
      }

      // 获取表格的根DOM元素
        if (currentCell.closest('tr')) {
    const endCell = getCellPosition(currentCell)
    const item = tableData.value[startCell.rowIndex].cellEvents
    if (item?.[columns[endCell.colIndex - 1].prop]?.events.has('booked')) {
      ElMessage({
        message: t('bookInstruments.bookingConflict'),
        grouping: true,
        type: 'warning'
      })
      handleRelease()
    }
    if (startCell && endCell) {
      const cellsInRange = calculateRange(startCell, endCell)
      highlightCells(cellsInRange, startCell.rowIndex)
    }
  }
  e.stopPropagation()
}
    const handleRelease = () => {
      isDragging = false
      startCell = null
      // 移除鼠标图标切换
      document.querySelectorAll('.el-table__row').forEach(cell => {
        cell.style.cursor = 'default'
      })
      // 解绑事件
      el.removeEventListener('mousemove', handleMouseMove)
      el.removeEventListener('mouseup', handleRelease)
    }
  }
}

// 获取单元格坐标
const getCellPosition = (cell) => {
  const rowIndex = cell.closest('tr').rowIndex
  const colIndex = Array.from(cell.closest('tr').children).indexOf(cell.closest('td'))
  return {
    rowIndex,
    colIndex
  }
}

// 计算选中范围
const calculateRange = (start, end) => {
  const row = start.rowIndex
  const minCol = Math.min(start.colIndex, end.colIndex)
  const maxCol = Math.max(start.colIndex, end.colIndex)
  const cells = []
  for (let c = minCol; c <= maxCol; c++) {
    cells.push(`${row}-${c}`)
  }
  return cells
}

// 高亮单元格
const highlightCells = (cells, rowIndex) => {
  const item = tableData.value[rowIndex].cellEvents
  // 获取表格的根DOM元素
  const tableEl = bookingDetailTable.value?.$el
  if (!tableEl) return
  // 获取所有行元素（Element Plus 的类名为 el-table__row）
  const rows = tableEl.querySelectorAll('.el-table__row')[rowIndex]
  for (let i = 0; i < rows.querySelectorAll('.el-table__cell').length - 1; i++) {
    const cell = rows.querySelectorAll('.el-table__cell')[i + 1]
    // 只在当前格子事件只有white，表明为空白行时添加背景选中色
    if (item?.[columns[i].prop]?.events.size === 1 && item?.[columns[i].prop]?.events.values().next().value === 'white') {
      const pos = getCellPosition(cell)
      const isSelected = cells.includes(`${pos.rowIndex}-${pos.colIndex}`)
      cell.classList.toggle('selected-cell', isSelected)
      if (isSelected) selectedCells.value.add(`${pos.rowIndex}-${pos.colIndex}`)
    }
  }
}

const getTime = (index: number) => {
  const week = Math.floor(index/12)
  const hourStr = (index%12*2).toString().padStart(2, '0') // 使用padStart补零，确保小时始终是两位数
  return `${props.weekDate[week]} ${hourStr}:00:00`
}

const getDateYMD = (time: Date) => {
  const year = time.getFullYear()
  const month = String(time.getMonth() + 1).padStart(2, '0') // 月份补零
  const day = String(time.getDate()).padStart(2, '0') // 日补零
  return `${year}-${month}-${day}`
}
const showTimeLine = (prop: string, isTitle: number) => {
  if (!props.weekDate.includes(getDateYMD(timeNow.value))) {
    return false
  }
  const hours = timeNow.value.getHours() // 获取当前小时（0-23）
  const week = timeNow.value.getDay() - 1 // 获取当前周
  const final = Math.floor(hours / 2) + week * 12 + isTitle
  if (isTitle) {
    if (hours < 2) {
      return prop === columns[final - 1].prop
    } else if (hours >= 2 && hours < 4) {
      return prop === columns[final - 2].prop
    }
  }
  return prop === columns[final].prop
}
const redDotStyle = (prop: string) => {
  const hours = timeNow.value.getHours() // 获取当前小时（0-23）
  const minutes = timeNow.value.getMinutes() // 获取当前分钟（0-59）
  const percent = hours % 2 / 2 + minutes / 120
  const week = timeNow.value.getDay() - 1 // 获取当前分钟（0-59）
  const final = Math.floor(hours / 2) + week * 12 + 1
  // 避免被遮挡
  if (hours < 2) {
    if (prop === columns[final - 1].prop) {
      return {
        left: `calc(-20% + ${percent * 100}% - 3.5px)`,
        bottom: '-12px'
      }
    }
  } else if (hours >= 2 && hours < 4) {
    if (prop === columns[final - 2].prop) {
      return {
        left: `calc(80% + ${percent * 100}% - 3.5px)`,
        bottom: '-12px'
      }
    }
  } else {
    if (prop === columns[final].prop) {
      if (hours >= 22 && hours < 24) {
        return {
          left: `calc( -120% + ${percent * 100}% - 3.5px)`,
          bottom: '-12px'
        }
      }
      return {
        left: `calc( -100% + ${percent * 100}% - 3.5px)`
      }
    }
  }
  return ''
}

const redLineStyle = computed(() => {
  const hours = timeNow.value.getHours() // 获取当前小时（0-23）
  const minutes = timeNow.value.getMinutes() // 获取当前分钟（0-59）
  const percent = hours % 2 / 2 + minutes / 120
  return {
    left: `${percent * 100}%`
  }
})

const deleteInstrumentRecord = (bookedInfo: any) => {
  // 设置要删除的预约信息并显示对话框
  afterDeleteClose = false
  deleteBookingInfo.value = bookedInfo
  deleteDialogVisible.value = true
}

// 处理删除确认
const handleDeleteConfirm = async (bookingId: any) => {
  deleteLoading.value = true
  try {
    const url = '/?r=instrument-booking/delete-instrument-booking'
    const res = await axios.post(url, {
      id: bookingId,
      headers: {
        Accept: 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    })

    if (res.data.status === 1) {
      ElMessage({
        message: t('bookInstruments.deleteSuccess'),
        grouping: true,
        type: 'success'
      })
      // 删除成功了，该关闭popover框
      afterDeleteClose = true
      handlePrefixClick(false)
    } else {
      ElMessage({
        message: t('bookInstruments.deleteError'),
        grouping: true,
        type: 'error'
      })
    }
  } catch (error) {
    ElMessage({
      message: t('bookInstruments.deleteError'),
      grouping: true,
      type: 'error'
    })
  } finally {
    deleteLoading.value = false
    deleteDialogVisible.value = false
  }
}

// 处理删除取消
const handleDeleteCancel = () => {
  popoverVisible.value = !afterDeleteClose
  deleteDialogVisible.value = false
}

// 监听父组件传来的 weekDate 变化
watch(
    () => props.weekDate,
    (newVal, oldVal) => {
      let i = 0
      newVal.forEach(item => {
        columns[i].date = item.slice(-2)
        i += 12
      })
      handlePrefixClick(false)
    },
    {
      deep: true,
      immediate: true
    }
)

defineExpose({
  handlePrefixClick
})

onMounted(() => {
  document.addEventListener('click', handleDocumentClick)
})

onUnmounted(() => {
  document.removeEventListener('click', handleDocumentClick)
})
</script>

<style scoped>
.booked-details {
  :deep(.header-time-class) {
    .cell {
      position: relative;
      right: -20%;
      overflow: visible !important;
      white-space: nowrap;
    }

    height: 40px;
    font-size: 14px;
    font-weight: 400;
    color: #6A6A73;
    position: relative; /* 为标题绝对定位提供参照 */
  }

  :deep(.clear-header-border) {
    border-right: none !important;

    .cell {
      overflow: visible !important;
    }
  }

  :deep(.el-table__cell) {
    padding: 3px 0;
  }

  :deep(.el-divider--horizontal) {
    margin: 5px 0;
  }
}

.booked-details :deep(.clear-border-right) {
  border-right: none !important;
  z-index: 0;
}

.booked-details :deep(.default-cell-color) {
  background-color: #F2F2F580;
  background-size: 100% 100%;
  /* border-radius: 3px; */
  z-index: 0;
}

.booked-details :deep(.el-table__body tr:hover .default-cell-color) {
  background-color: #F2F2F580;
  /* background: linear-gradient(90deg,white 0%,black 20% 30%,white, black 60% 100%); */
}

.booked-details :deep(.el-table__body tr:hover .hover-cell-color:hover) {
  background-color: black;
}

.booked-details :deep(.el-table__body tr) {
  box-sizing: border-box;
  height: 48px !important; /* 直接设置行高 */
}

.booked-details :deep(.el-table__body tr:hover td) {
  background-color: inherit;
}

.booked-details :deep(.el-table__body .el-table__row) {
  transition: none !important;
}

.booked-details :deep(.el-table__body tr td:hover) {
  background-color: #F2F2F5;
}

.booked-details :deep(.el-table__border-left-patch) {
  display: none;
}

.booked-details :deep(.el-table--border:before), .booked-details :deep(.el-table--border:after) {
  height: 0;
}

.instrument-name {
  color: #103033;
  font-size: 14px;
}

.disable-text-select {
  -webkit-user-select: none; /* Safari/Chrome */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* IE/Edge */
  user-select: none; /* 标准语法 */
}

.instrument-code {
  color: #9C9CA6;
  font-size: 12px;
  line-height: 18px;
}

.instrument-name-pop {
  color: #103033;
  font-size: 16px;
  font-weight: 500;
  padding: 0 6px;
  line-height: 24px;
}

.instrument-code-pop {
  color: #9C9CA6;
  font-size: 14px;
  line-height: 22px;
  padding: 0 6px;
}

.booked-details :deep(th) {
  border-left: none !important;
  border-top: none !important;
  border-right: none !important;
}

.booked-details :deep(.el-table__body) {
  border-bottom: none;
}

.booked-details :deep(.el-table--border .el-table__inner-wrapper::after){
  height: 0;
}

.booked-details :deep(td) {
  border-top: none !important;
  border-left: none !important;
}

.search-instruments {
  width: 224px;
  height: 27px;
}

.search-instruments :deep(input), .search-instruments :deep(input):focus {
  border: none;
  padding: 0;
  box-shadow: none;
}

.search-instruments :deep(.el-input__wrapper) {
  box-shadow: none;
  padding-left: 20px;
}

.search-instruments :deep(.el-input__wrapper):hover {
  box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color));
}

.search-instruments :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px var(--el-color-primary) !important;
}

.instruments-divider {
  margin-bottom: 10px;
}

.instruments-divider :deep(.el-divider--horizontal) {
  margin: 10px 0;
}

.instruments-divider :deep(.el-descriptions__label) {
  font-size: 14px;
  color: #6A6A73;
  width: 100px;
  display: inline-block;
  line-height: 23px;
}

.instruments-divider :deep(.el-descriptions__content) {
  font-size: 14px;
  color: #303033;
  display: inline-block;
  line-height: 25px;
}

.instruments-divider :deep(.el-descriptions__body .el-descriptions__table:not(.is-bordered) .el-descriptions__cell) {
  padding-bottom: 0;
}

.instruments-divider :deep(.popover-state-class) {
  color: #58B23E;
}

td {
  background: linear-gradient(
      to right,
      transparent 84%,
      #ccc 84% /* 前84%透明，后16%灰色 */
  );
}

.blankCell {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.cell-tips {
  position: absolute;
  top: 0;
  font-size: 12px;
  display: inline-block;
  white-space: nowrap; /* 禁止换行 */
  overflow: hidden; /* 允许内容溢出 */
  text-overflow: ellipsis; /* 禁用省略号 */
  pointer-events: none; /* 阻止鼠标事件，让事件穿透到父元素 */
}

.red-timeline-offset {
  position: absolute;
  top: 0;
  height: calc(100% + 2px);
}

.red-timeline {
  width: 1px;
  background: #FF7733;
}

.show-red-line {
  content: '';
  position: absolute;
  width: 8px;
  overflow: visible !important;
  bottom: -12px;
  height: 8px;
  background: #ff4d4f;
  border-radius: 50%;
  display: block; /* 条件满足时显示 */
}

.red-dot {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #ff4d4f;
  border-radius: 50%;
  bottom: -4px;
  overflow: visible !important;
}

.booked-details :deep(.el-table__header-wrapper table) {
  border: none;
}

.instruments-divider:deep(.el-descriptions td) {
  border: none
}

.instruments-divider :deep( .el-descriptions__table) {
  border: none
}

.booked-details :deep(.selected-cell) {
  background: #7366FF80 !important;
  border: 1px solid #409eff80 !important;
  border-right: none !important;
}
.booking-info :deep(.el-descriptions__cell) {
  line-height: 30px;
}

.booking-info :deep(.eln-page) {
  padding: 0 5px;
  display: inline-block;
  background-color: #E8EAFC;
  border-radius: 4px;
  margin: 0 2px;
}

/* 图标垂直对齐调整 */
.icon-align {
  vertical-align: middle;
  transform: translateY(-1px); /* 向上调整1px，可根据需要调整 */
  color: #B6B6BF;
  width: 14px;
  margin-right: 10px;
}

.flex-space {
  display: flex;
  justify-content: space-between;
  line-height: 24px;
}

.top-icon :deep(.el-icon) {
  width: 30px;
  transform: translateY(3px);
}

.top-icon :deep(.el-icon svg) {
  width: 18px;
  height: 18px;
  color: #6A6A73;
  cursor: pointer;
}
</style>
